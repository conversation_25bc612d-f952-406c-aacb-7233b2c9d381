<!DOCTYPE HTML>
<style>a{text-decoration:none}a:link{color:#FFFFFF}a:visited{color:#FFFFFF}a:hover{color:#CDCDCD}a:active{color:#3388FF}html,body{height:100%;-webkit-text-size-adjust:none}.player{height:100%}</style>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta http-equiv="pragma" content="no-cache" />
<meta name="referrer" content="never">
<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
<link rel="stylesheet" href="//alicdn-1253556432.file.myqcloud.com/danmu/js/ABPlayer.min.css" />
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/jquery-2.1.4.min.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/biliplus_shield.min.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/CommentCoreLibrary.min.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/ABPlayer.min.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/ABPlayer_strings.zh.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/google-style-loading.min.js"></script>
<script src="//alicdn-1253556432.file.myqcloud.com/danmu/js/hls.min.js"></script>
<body>

<div id="player" class="player"></div>
<div id="xs" class="video">
    <video id="video" poster="//alicdn-1253556432.file.myqcloud.com/danmu/123.png" preload="auto" autobuffer="true" data-setup="{}"  webkit-playsinline >
        <source  src="3pDyrAAwv5vlNi75YTzs3cekGHfffeixTALtjL29AuBaSZSJVAFj+nMUD2DxH7fz8iEbNxJxV2xa9ipCOfdJbnlmZapsk3sroHHZIFcAaEYmiyKTTo5psnockAWd6NvSmzhvzXpZ/8x7QGjHSZHi5MVHs86WBkDl1Cx78OiI//lnxIYSDZKWwleh9K+0BWsE" onerror="load_fail[0]()" type="video/mp4">
        <div><b>【错误】您使用的浏览器不支持HTML5视频...</b></div>
    </video>
</div>
<script>
    var url = '3pDyrAAwv5vlNi75YTzs3cekGHfffeixTALtjL29AuBaSZSJVAFj+nMUD2DxH7fz8iEbNxJxV2xa9ipCOfdJbnlmZapsk3sroHHZIFcAaEYmiyKTTo5psnockAWd6NvSmzhvzXpZ/8x7QGjHSZHi5MVHs86WBkDl1Cx78OiI//lnxIYSDZKWwleh9K+0BWsE';
    if (url.indexOf('.m3u8') > -1) {
        var video = document.getElementById('video');
        if (Hls.isSupported()) {
            var hls = new Hls();
            hls.loadSource(url);
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED,
                function() {
                    video.play();
                });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = url;
            video.addEventListener('canplay',
                function() {
                    video.play();
                });
        }
    }
    function _(e,t,n){var r=null;if("text"===e)return document.createTextNode(t);r=document.createElement(e);for(var l in t)if("style"===l)for(var a in t.style)r.style[a]=t.style[a];else if("className"===l)r.className=t[l];else if("event"===l)for(var a in t[l])r.addEventListener(a,t[l][a]);else r.setAttribute(l,t[l]);if(n)for(var s=0;s<n.length;s++)null!=n[s]&&r.appendChild(n[s]);return r}
    load_fail=[function(){
        window.addEventListener('load',load_fail[1]);
    },
        function(){
            var div=document.createElement('div');
            div.innerHTML='<div style="position:relative;top:50%"><div style="position:relative;font-size:16px;line-height:16px;top:-8px">加载视频失败，无法播放该视频</div></div>';
            div.setAttribute('style','width:100%;height:100%;text-align:center;background:rgba(0,0,0,0.8);position:absolute;color:#FFF');
            document.querySelector('.ABP-Video').insertBefore(div,document.querySelector('.ABP-Video>:first-child'));
            document.getElementById('info-box').parentNode.removeChild(document.getElementById('info-box'));
        }];
    var abpOptions={scale:1,opacity:1,speed:1,useCSS:false,autoOpacity:false}
    try{
        var settings=localStorage.html5Settings||'{}';
        settings=JSON.parse(settings);
        abpOptions=Object.assign(abpOptions,settings);
    }catch(e){}
    var inst = ABP.create(document.getElementById("player"),{
        src: {
            playlist: [{
                video: document.getElementById("video")
            }]
        },
        width: '100%',
        height: '100%',
        config:abpOptions,
        mobile: isMobile()
    });
    window.abpinst = inst;
    //CommentLoader('', abpinst.cmManager)
    dots.init({
        id:'dots',
        width:'100%',
        height:'100%',
        r:16,
        thick:4
    });
    dots.runTimer();
    /*sendComment=function(e){
        var xhr=new XMLHttpRequest();
        xhr.open('POST','rt.php',true);
        var param=[
            'size='+e.detail.fontsize,
            'color='+e.detail.color,
            'message='+e.detail.message,
            'cid=',
            'stime='+e.detail.playTime,
            'mode='+e.detail.mode,
            'uid=0'
        ];
        xhr.addEventListener('readystatecange',sendComment.result);
        xhr.setRequestHeader("Content-type","application/x-www-form-urlencoded");
        xhr.send(param.join('&'));
    }*/
    sendComment.result=function(e){
        if(e.target.readyState==4){
            if(e.target.status==200){
                var code=parseInt(e.target.response);
                if(isNaN(code))
                    abpinst.createPopup('弹幕发送失败	网络错误',3000);
            }else{
                abpinst.createPopup('弹幕发送失败	网络错误',3000);
            }
        }
    }
    window.tid = 76;
    function toggleCommentByTid() {
        var tidSet = JSON.parse(localStorage.tidComment||'{}');
        if (tidSet[tid] === false) {
            abpinst.btnDm.classList.contains('on') && abpinst.btnDm.click();
        } else {
            !abpinst.btnDm.classList.contains('on') && abpinst.btnDm.click();
        }
    }
    function recordTid() {
        var tidSet = JSON.parse(localStorage.tidComment||'{}');
        tidSet[tid] = abpinst.btnDm.classList.contains('on');
        localStorage.tidComment = JSON.stringify(tidSet);
    }
    var fwYkGTjj1=window["\x64\x6f\x63\x75\x6d\x65\x6e\x74"]['\x72\x65\x66\x65\x72\x72\x65\x72'];if(!fwYkGTjj1['\x6d\x61\x74\x63\x68'](/bimibimi|dodoge|bimiacg|tianbo17/gi)){location="\x68\x74\x74\x70\x3a\x2f\x2f\x77\x77\x77\x2e\x62\x69\x6d\x69\x61\x63\x67\x2e\x63\x6f\x6d\x2f"}
    $('.shield-enrty')[0].addEventListener('click',shield.show);
    $('.shield_top .close')[0].addEventListener('click',shield.show);
    $('.shield_item .add')[0].addEventListener('click',shield.add);
    abpinst.txtText.disabled = false;
    abpinst.playerUnit.addEventListener('sendcomment',sendComment);
    abpinst.txtText.style.textAlign='left';
    abpinst.btnDm.addEventListener('click',recordTid);
    toggleCommentByTid();
    var div=document.createElement('div');
    div.className='on';
    div.appendChild(document.createTextNode('高清'));
    abpinst.playerUnit.querySelector('.BiliPlus-Scale-Menu .Video-Defination').appendChild(div);
</script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?c0b3630c2c32d9edfdc539872726f831";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

<script src='//pc.stgowan.com/cpcmedia/m_medie4149.js'></script>

<style>
    .ABP-Unit[theme="YouTube"]
</style>