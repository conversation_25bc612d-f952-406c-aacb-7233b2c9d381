# -*- coding: utf-8 -*-
# NO视频插件测试脚本
import sys
import os
sys.path.append('.')

def test_spider():
    """测试NO视频插件的所有功能"""
    print("=" * 60)
    print("开始测试NO视频插件")
    print("=" * 60)
    
    try:
        # 导入插件
        from plugin.html.NO视频 import Spider
        spider = Spider()
        spider.init()
        
        print(f"插件名称: {spider.getName()}")
        print()
        
        # 测试首页内容
        print("1. 测试首页内容...")
        home_result = spider.homeContent({})
        print(f"分类数量: {len(home_result.get('class', []))}")
        print(f"首页视频数量: {len(home_result.get('list', []))}")
        
        if home_result.get('class'):
            print("分类列表:")
            for i, cat in enumerate(home_result['class'][:5]):
                print(f"  {i+1}. {cat['type_name']}: {cat['type_id']}")
        
        if home_result.get('list'):
            print("首页视频示例:")
            for i, video in enumerate(home_result['list'][:3]):
                print(f"  {i+1}. {video['vod_name']}")
                print(f"     ID: {video['vod_id']}")
                print(f"     图片: {video['vod_pic']}")
        print()
        
        # 测试分类内容
        print("2. 测试分类内容...")
        if home_result.get('class'):
            test_category = home_result['class'][0]['type_id']
            category_result = spider.categoryContent(test_category, '1', {}, {})
            print(f"分类 '{test_category}' 视频数量: {len(category_result.get('list', []))}")
            print(f"页码: {category_result.get('page', 0)}")
            print(f"总页数: {category_result.get('pagecount', 0)}")
            
            if category_result.get('list'):
                print("分类视频示例:")
                for i, video in enumerate(category_result['list'][:3]):
                    print(f"  {i+1}. {video['vod_name']}")
                    print(f"     ID: {video['vod_id']}")
        print()
        
        # 测试搜索功能
        print("3. 测试搜索功能...")
        search_result = spider.searchContent('美剧', False, '1')
        print(f"搜索结果数量: {len(search_result.get('list', []))}")
        print(f"搜索页码: {search_result.get('page', 0)}")
        
        if search_result.get('list'):
            print("搜索结果示例:")
            for i, video in enumerate(search_result['list'][:3]):
                print(f"  {i+1}. {video['vod_name']}")
                print(f"     ID: {video['vod_id']}")
        print()
        
        # 测试详情和播放
        test_video = None
        if home_result.get('list'):
            test_video = home_result['list'][0]
        elif category_result.get('list'):
            test_video = category_result['list'][0]
        elif search_result.get('list'):
            test_video = search_result['list'][0]
        
        if test_video:
            print("4. 测试详情页面...")
            test_id = test_video['vod_id']
            print(f"测试视频: {test_video['vod_name']} (ID: {test_id})")
            
            detail_result = spider.detailContent([test_id])
            if detail_result.get('list'):
                detail = detail_result['list'][0]
                print(f"详情标题: {detail['vod_name']}")
                print(f"详情图片: {detail['vod_pic']}")
                print(f"播放源: {detail['vod_play_from']}")
                print(f"播放列表: {detail['vod_play_url'][:100]}...")
                
                # 测试播放解析
                print("\n5. 测试播放解析...")
                if detail['vod_play_url']:
                    # 提取第一个播放地址
                    play_sources = detail['vod_play_from'].split('$$$')
                    play_urls = detail['vod_play_url'].split('$$$')
                    
                    if play_urls and play_urls[0]:
                        first_episode = play_urls[0].split('#')[0]
                        if '$' in first_episode:
                            episode_name, episode_url = first_episode.split('$', 1)
                            print(f"测试播放: {episode_name}")
                            
                            player_result = spider.playerContent(play_sources[0] if play_sources else '', episode_url, [])
                            print(f"解析模式: {player_result.get('parse', 'unknown')}")
                            print(f"播放地址: {player_result.get('url', 'none')}")
                            print(f"请求头: {bool(player_result.get('header'))}")
            print()
        
        print("=" * 60)
        print("NO视频插件测试完成")
        print("=" * 60)
        
        # 生成测试报告
        report = {
            "插件名称": spider.getName(),
            "首页分类数": len(home_result.get('class', [])),
            "首页视频数": len(home_result.get('list', [])),
            "分类视频数": len(category_result.get('list', [])) if 'category_result' in locals() else 0,
            "搜索结果数": len(search_result.get('list', [])),
            "详情测试": "成功" if test_video and detail_result.get('list') else "失败",
            "播放测试": "成功" if test_video and 'player_result' in locals() else "失败"
        }
        
        print("测试报告:")
        for key, value in report.items():
            print(f"  {key}: {value}")
        
        return report
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_spider()
