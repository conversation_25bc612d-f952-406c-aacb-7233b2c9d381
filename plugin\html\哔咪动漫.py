# -*- coding: utf-8 -*-
# 哔咪动漫插件 - bimiacg11.net
import re
import json
import time
from urllib.parse import quote, unquote, urljoin
from pyquery import PyQuery as pq
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        pass

    def getName(self):
        return "哔咪动漫"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def action(self, action):
        pass

    def destroy(self):
        pass

    host = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    def homeContent(self, filter):
        try:
            response = self.fetch(self.host, headers=self.headers)
            data = self.getpq(response.text)

            result = {}
            classes = []

            # 添加分类 - 基于网站导航菜单
            categories = [
                {'type_name': '新番放送', 'type_id': 'riman'},
                {'type_name': '大陆动漫', 'type_id': 'guoman'},
                {'type_name': '番组计划', 'type_id': 'fanzu'},
                {'type_name': '剧场动画', 'type_id': 'juchang'},
                {'type_name': '番组2', 'type_id': 'fanzu2'},
                {'type_name': '电影', 'type_id': 'move'},
                {'type_name': '剧集', 'type_id': 'dianshiju'}
            ]
            classes.extend(categories)

            # 获取首页推荐内容 - 今日热播
            videos = []
            # 查找今日热播列表
            hot_items = data('.module-item, .video-item, .item')

            for item in hot_items.items():
                try:
                    # 查找链接
                    link_elem = item('a').eq(0)
                    detail_link = link_elem.attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id_match = re.search(r'/bangumi/bi/(\d+)/', detail_link)
                    if not vod_id_match:
                        continue
                    vod_id = vod_id_match.group(1)

                    # 提取标题
                    vod_name = link_elem.text().strip()
                    if not vod_name:
                        vod_name = link_elem.attr('title') or ''

                    # 清理标题中的多余文本
                    vod_name = re.sub(r'\s+', ' ', vod_name).strip()

                    # 提取图片
                    img_elem = item('img').eq(0)
                    vod_pic = img_elem.attr('src') or img_elem.attr('data-src') or img_elem.attr('data-original')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = urljoin(self.host, vod_pic)

                    # 提取备注信息
                    vod_remarks = ''
                    remarks_elem = item('.remarks, .note, .status')
                    if remarks_elem:
                        vod_remarks = remarks_elem.text().strip()

                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    if vod_name and vod_id:
                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': vod_name,
                            'vod_pic': vod_pic,
                            'vod_year': vod_year,
                            'vod_remarks': vod_remarks
                        })
                except Exception as e:
                    self.log(f"解析首页视频项失败: {e}")
                    continue

            result['class'] = classes
            result['list'] = videos
            return result

        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return {'class': [], 'list': []}

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        try:
            # 构建分类页面URL
            url = f"{self.host}/type/{tid}/"
            if int(pg) > 1:
                url = f"{self.host}/type/{tid}-{pg}/"

            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)

            videos = []
            # 查找视频列表项
            items = data('.module-item, .video-item, .item')

            for item in items.items():
                try:
                    # 查找链接
                    link_elem = item('a').eq(0)
                    detail_link = link_elem.attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id_match = re.search(r'/bangumi/bi/(\d+)/', detail_link)
                    if not vod_id_match:
                        continue
                    vod_id = vod_id_match.group(1)

                    # 提取标题
                    vod_name = link_elem.text().strip()
                    if not vod_name:
                        vod_name = link_elem.attr('title') or ''

                    # 清理标题中的多余文本
                    vod_name = re.sub(r'\s+', ' ', vod_name).strip()

                    # 提取图片
                    img_elem = item('img').eq(0)
                    vod_pic = img_elem.attr('src') or img_elem.attr('data-src') or img_elem.attr('data-original')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = urljoin(self.host, vod_pic)

                    # 提取备注信息
                    vod_remarks = ''
                    remarks_elem = item('.remarks, .note, .status')
                    if remarks_elem:
                        vod_remarks = remarks_elem.text().strip()

                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    if vod_name and vod_id:
                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': vod_name,
                            'vod_pic': vod_pic,
                            'vod_year': vod_year,
                            'vod_remarks': vod_remarks
                        })
                except Exception as e:
                    self.log(f"解析分类视频项失败: {e}")
                    continue

            # 分析分页信息
            pagecount = 1
            page_links = data('.pagination a, .page a, .pager a')
            if page_links:
                for link in page_links.items():
                    href = link.attr('href')
                    if href:
                        page_match = re.search(r'-(\d+)/', href)
                        if page_match:
                            page_num = int(page_match.group(1))
                            if page_num > pagecount:
                                pagecount = page_num

            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': max(pagecount, int(pg)),
                'limit': 24,
                'total': len(videos) * pagecount
            }
            return result

        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 0, 'limit': 24, 'total': 0}

    def detailContent(self, ids):
        try:
            vod_id = ids[0]
            url = f"{self.host}/bangumi/bi/{vod_id}/"
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)

            # 提取基本信息
            title = data('h1').eq(0).text().strip()
            if not title:
                title = data('title').text().split('-')[0].strip()

            # 提取图片
            pic = data('img[alt*="' + title + '"], img').eq(0).attr('src')
            if pic and not pic.startswith('http'):
                pic = urljoin(self.host, pic)

            # 提取详细信息
            vod_year = ''
            vod_area = ''
            vod_lang = ''
            vod_director = ''
            vod_actor = ''
            type_name = ''
            vod_content = ''

            # 查找信息列表项
            info_items = data('li')
            for item in info_items.items():
                item_text = item.text().strip()

                if item_text.startswith('年份：'):
                    vod_year = item_text.replace('年份：', '').strip()
                elif item_text.startswith('地区：'):
                    # 提取地区链接文本
                    area_link = item('a').eq(0)
                    if area_link:
                        vod_area = area_link.text().strip()
                    else:
                        vod_area = item_text.replace('地区：', '').strip()
                elif item_text.startswith('语言：'):
                    vod_lang = item_text.replace('语言：', '').strip()
                elif item_text.startswith('导演：'):
                    # 提取导演链接文本
                    director_link = item('a').eq(0)
                    if director_link:
                        vod_director = director_link.text().strip()
                    else:
                        vod_director = item_text.replace('导演：', '').strip()
                elif item_text.startswith('声优：'):
                    vod_actor = item_text.replace('声优：', '').strip()
                elif item_text.startswith('类型：'):
                    # 提取所有类型链接
                    type_links = item('a')
                    types = []
                    for link in type_links.items():
                        types.append(link.text().strip())
                    type_name = '/'.join(types) if types else item_text.replace('类型：', '').strip()
                elif item_text.startswith('简介：'):
                    # 获取简介内容，可能在下一个段落中
                    content_p = item.next('p')
                    if content_p:
                        vod_content = content_p.text().strip()

            # 如果没有在li中找到简介，尝试其他方式
            if not vod_content:
                # 查找剧集简介
                content_elem = data('p').filter(lambda i, e: '因冤罪失业' in pq(e).text() or len(pq(e).text()) > 50)
                if content_elem:
                    vod_content = content_elem.eq(0).text().strip()

            # 提取播放列表
            play_sources = []
            play_urls = []

            # 方法1: 查找播放源标题（如"线路：Danma L"）
            source_elements = data('div, span, p').filter(lambda i, e: '线路：' in pq(e).text())

            if source_elements:
                for source_elem in source_elements.items():
                    source_text = source_elem.text().strip()
                    if '线路：' in source_text:
                        # 提取线路名称
                        source_name = source_text.replace('线路：', '').strip()
                        if source_name:
                            play_sources.append(source_name)

                            # 查找该播放源下的剧集列表
                            episodes = []

                            # 查找同级或下级的播放链接列表
                            episode_container = source_elem.next('ul, ol, div')
                            if not episode_container or len(episode_container) == 0:
                                # 如果没找到下级容器，查找父级容器中的链接
                                episode_container = source_elem.parent()

                            if episode_container:
                                episode_links = episode_container.find('a[href*="/play/"]')
                                for ep in episode_links.items():
                                    ep_name = ep.text().strip()
                                    ep_url = ep.attr('href')
                                    if ep_name and ep_url and ('第' in ep_name or '话' in ep_name or ep_name.isdigit()):
                                        episodes.append(f"{ep_name}${ep_url}")

                            # 如果还没找到，在整个页面中查找播放链接
                            if not episodes:
                                all_play_links = data('a[href*="/play/"]')
                                for ep in all_play_links.items():
                                    ep_name = ep.text().strip()
                                    ep_url = ep.attr('href')
                                    if ep_name and ep_url and ('第' in ep_name or '话' in ep_name):
                                        episodes.append(f"{ep_name}${ep_url}")

                            play_urls.append('#'.join(episodes))
                            break  # 只处理第一个找到的播放源

            # 方法2: 如果没找到播放源，使用默认方式
            if not play_sources:
                all_play_links = data('a[href*="/play/"]')
                if all_play_links:
                    play_sources.append("默认线路")
                    episodes = []
                    for ep in all_play_links.items():
                        ep_name = ep.text().strip()
                        ep_url = ep.attr('href')
                        if ep_name and ep_url and ('第' in ep_name or '话' in ep_name or ep_name.isdigit()):
                            episodes.append(f"{ep_name}${ep_url}")
                    play_urls.append('#'.join(episodes))

            vod = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': vod_year,
                'vod_area': vod_area,
                'vod_lang': vod_lang,
                'vod_director': vod_director,
                'vod_actor': vod_actor,
                'vod_content': vod_content,
                'vod_play_from': '$$$'.join(play_sources),
                'vod_play_url': '$$$'.join(play_urls)
            }

            return {'list': [vod]}

        except Exception as e:
            self.log(f"获取详情内容失败: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        try:
            # 构建搜索URL - 使用POST方式搜索
            search_url = f"{self.host}/vod/search/"
            search_data = {'wd': key}

            response = self.post(search_url, headers=self.headers, data=search_data)
            data = self.getpq(response.text)

            videos = []
            # 查找搜索结果项
            items = data('.module-item, .video-item, .search-item, .item')

            for item in items.items():
                try:
                    # 查找链接
                    link_elem = item('a').eq(0)
                    detail_link = link_elem.attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id_match = re.search(r'/bangumi/bi/(\d+)/', detail_link)
                    if not vod_id_match:
                        continue
                    vod_id = vod_id_match.group(1)

                    # 提取标题
                    vod_name = link_elem.text().strip()
                    if not vod_name:
                        vod_name = link_elem.attr('title') or ''

                    # 清理标题中的多余文本
                    vod_name = re.sub(r'\s+', ' ', vod_name).strip()

                    # 提取图片
                    img_elem = item('img').eq(0)
                    vod_pic = img_elem.attr('src') or img_elem.attr('data-src') or img_elem.attr('data-original')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = urljoin(self.host, vod_pic)

                    # 提取备注信息
                    vod_remarks = ''
                    remarks_elem = item('.remarks, .note, .status')
                    if remarks_elem:
                        vod_remarks = remarks_elem.text().strip()

                    # 提取年份
                    year_elem = item('.year, .date')
                    vod_year = ''
                    if year_elem:
                        year_text = year_elem.text()
                        year_match = re.search(r'(\d{4})', year_text)
                        if year_match:
                            vod_year = year_match.group(1)

                    if vod_name and vod_id:
                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': vod_name,
                            'vod_pic': vod_pic,
                            'vod_year': vod_year,
                            'vod_remarks': vod_remarks
                        })
                except Exception as e:
                    self.log(f"解析搜索结果项失败: {e}")
                    continue

            return {'list': videos, 'page': int(pg)}

        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {'list': [], 'page': int(pg)}

    def decrypt_video_url(self, encrypted_url, encrypt_type):
        """根据encrypt类型解密视频URL"""
        try:
            if encrypt_type == '0':
                # encrypt为0，直接返回原URL
                return encrypted_url
            elif encrypt_type == '1':
                # encrypt为1，只需要unescape
                return unquote(encrypted_url)
            elif encrypt_type == '2':
                # encrypt为2，先base64解码再unescape
                import base64
                try:
                    # 添加padding
                    padded = encrypted_url + '=' * (4 - len(encrypted_url) % 4)
                    decoded_bytes = base64.b64decode(padded)

                    # 尝试不同的编码方式
                    for encoding in ['utf-8', 'latin-1', 'ascii', 'gbk']:
                        try:
                            decoded_str = decoded_bytes.decode(encoding)
                            unescaped = unquote(decoded_str)

                            # 检查解码结果是否合理
                            if any(indicator in unescaped.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv', '/']):
                                self.log(f"使用{encoding}编码解密成功")
                                return unescaped
                        except:
                            continue

                    # 如果所有编码都失败，尝试直接返回latin-1解码结果
                    try:
                        decoded_str = decoded_bytes.decode('latin-1')
                        return unquote(decoded_str)
                    except Exception as e:
                        self.log(f"所有编码方式都失败: {e}")
                        return None

                except Exception as e:
                    self.log(f"base64解码失败: {e}")
                    return None

            return None

        except Exception as e:
            self.log(f"解密URL失败: {e}")
            return None

    def playerContent(self, flag, id, vipFlags):
        try:
            play_url = f"{self.host}{id}"
            response = self.fetch(play_url, headers=self.headers)
            content = response.text

            # 方法1: 分析player_aaaa变量获取播放配置
            player_patterns = [
                r'var\s+player_aaaa\s*=\s*({[^;]+});',
                r'player_aaaa\s*=\s*({[^;]+});',
                r'var\s+player_aaaa\s*=\s*({.*?});'
            ]

            player_match = None
            for pattern in player_patterns:
                player_match = re.search(pattern, content, re.DOTALL)
                if player_match:
                    break

            if player_match:
                try:
                    player_json_str = player_match.group(1)
                    self.log(f"找到player_aaaa配置")

                    # 提取关键信息
                    encrypt_match = re.search(r'"encrypt"[:\s]*(\d+)', player_json_str)
                    url_match = re.search(r'"url"[:\s]*"([^"]+)"', player_json_str)
                    link_match = re.search(r'"link"[:\s]*"([^"]+)"', player_json_str)

                    encrypt_type = encrypt_match.group(1) if encrypt_match else '0'
                    encrypted_url = url_match.group(1) if url_match else None
                    play_link = link_match.group(1) if link_match else None

                    self.log(f"encrypt类型: {encrypt_type}, 加密URL长度: {len(encrypted_url) if encrypted_url else 0}")

                    if encrypted_url:
                        # 尝试解密URL
                        decrypted_url = self.decrypt_video_url(encrypted_url, encrypt_type)

                        if decrypted_url and decrypted_url != encrypted_url:
                            self.log(f"解密成功: {decrypted_url[:100]}...")

                            # 检查解密后的URL是否是有效的视频地址
                            if any(ext in decrypted_url.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                                # 构建完整URL
                                if not decrypted_url.startswith('http'):
                                    if decrypted_url.startswith('/'):
                                        video_url = f"{self.host}{decrypted_url}"
                                    else:
                                        video_url = f"{self.host}/{decrypted_url}"
                                else:
                                    video_url = decrypted_url

                                self.log(f"找到直播流地址: {video_url}")
                                return {
                                    "parse": 0,
                                    "url": video_url,
                                    "header": self.headers
                                }

                        # 如果解密失败或不是视频URL，尝试弹幕播放器
                        danmu_urls = [
                            f"{self.host}/static/danmu/qy.php?url={encrypted_url}",
                            f"{self.host}/static/danmu/play.php?url={encrypted_url}"
                        ]
                    elif play_link:
                        play_link = play_link.replace('\\/', '/')
                        if not play_link.startswith('http'):
                            play_link = urljoin(self.host, play_link)
                        danmu_urls = [
                            f"{self.host}/static/danmu/qy.php?url={play_link}",
                            f"{self.host}/static/danmu/play.php?url={play_link}"
                        ]
                    else:
                        danmu_urls = []

                    # 尝试弹幕播放器接口
                    for danmu_url in danmu_urls:
                                try:
                                    self.log(f"尝试弹幕接口: {danmu_url}")
                                    danmu_response = self.fetch(danmu_url, headers=self.headers)

                                    if danmu_response.status_code == 200:
                                        danmu_content = danmu_response.text
                                        self.log(f"弹幕接口响应长度: {len(danmu_content)}")

                                        # 查找弹幕播放器中的video标签和source
                                        video_tag_pattern = r'<video[^>]*>.*?<source[^>]+src=["\']([^"\']+)["\'][^>]*>.*?</video>'
                                        video_match = re.search(video_tag_pattern, danmu_content, re.DOTALL)

                                        if video_match:
                                            source_url = video_match.group(1)
                                            self.log(f"从video标签中提取到source URL: {source_url[:100]}...")

                                            # 尝试解密source URL
                                            decrypted_source = self.decrypt_video_url(source_url, '2')  # 尝试base64+unescape解密

                                            if decrypted_source and decrypted_source != source_url:
                                                self.log(f"source URL解密成功: {decrypted_source[:100]}...")

                                                # 检查是否是有效的视频地址
                                                if any(ext in decrypted_source.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                                                    # 构建完整URL
                                                    if not decrypted_source.startswith('http'):
                                                        if decrypted_source.startswith('/'):
                                                            video_url = f"{self.host}{decrypted_source}"
                                                        else:
                                                            video_url = f"{self.host}/{decrypted_source}"
                                                    else:
                                                        video_url = decrypted_source

                                                    self.log(f"找到解密后的视频流地址: {video_url}")
                                                    return {
                                                        "parse": 0,
                                                        "url": video_url,
                                                        "header": self.headers
                                                    }

                                            # 如果解密失败，返回弹幕播放器URL
                                            self.log(f"source URL解密失败，返回弹幕播放器")
                                            return {
                                                "parse": 1,
                                                "url": danmu_url,
                                                "header": self.headers
                                            }

                                        # 查找其他可能的播放地址模式
                                        video_patterns = [
                                            r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
                                            r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
                                            r'(https?://[^"\'>\s]+\.flv[^"\'>\s]*)',
                                            r'hls\.loadSource\(["\']([^"\']+)["\']',
                                            r'video\.src\s*=\s*["\']([^"\']+)["\']'
                                        ]

                                        for video_pattern in video_patterns:
                                            video_matches = re.findall(video_pattern, danmu_content)
                                            if video_matches:
                                                for video_url in video_matches:
                                                    if video_url.startswith('http') and video_url.endswith(('.m3u8', '.mp4', '.flv')):
                                                        self.log(f"通过模式匹配找到播放地址: {video_url}")
                                                        return {
                                                            "parse": 0,
                                                            "url": video_url,
                                                            "header": self.headers
                                                        }

                                        # 如果没找到直播流，但返回了播放器页面，返回弹幕播放器URL
                                        if 'player' in danmu_content.lower() or 'video' in danmu_content.lower():
                                            self.log(f"弹幕接口返回播放器页面，使用该URL: {danmu_url}")
                                            return {
                                                "parse": 1,
                                                "url": danmu_url,
                                                "header": self.headers
                                            }

                                except Exception as e:
                                    self.log(f"弹幕接口 {danmu_url} 请求失败: {e}")

                except Exception as e:
                    self.log(f"解析player_aaaa失败: {e}")

            # 方法2: 直接查找页面中的播放地址
            video_patterns = [
                r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
                r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
                r'(https?://[^"\'>\s]+\.flv[^"\'>\s]*)'
            ]

            for pattern in video_patterns:
                match = re.search(pattern, content)
                if match:
                    video_url = match.group(1)
                    self.log(f"直接找到播放地址: {video_url}")
                    return {
                        "parse": 0,
                        "url": video_url,
                        "header": self.headers
                    }

            # 方法3: 查找iframe播放器
            iframe_pattern = r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>'
            iframe_match = re.search(iframe_pattern, content)

            if iframe_match:
                iframe_url = iframe_match.group(1)
                if not iframe_url.startswith('http'):
                    iframe_url = urljoin(self.host, iframe_url)

                self.log(f"找到iframe播放器: {iframe_url}")

                # 如果iframe URL不同于当前播放页面，尝试获取其内容
                if iframe_url != play_url:
                    try:
                        iframe_response = self.fetch(iframe_url, headers=self.headers)
                        iframe_content = iframe_response.text

                        # 在iframe中查找播放地址
                        for pattern in video_patterns:
                            match = re.search(pattern, iframe_content)
                            if match:
                                video_url = match.group(1)
                                self.log(f"iframe中找到播放地址: {video_url}")
                                return {
                                    "parse": 0,
                                    "url": video_url,
                                    "header": self.headers
                                }
                    except Exception as e:
                        self.log(f"获取iframe内容失败: {e}")

            # 方法4: 尝试通过播放器配置文件获取信息
            try:
                config_url = f"{self.host}/static/js/playerconfig.js"
                config_response = self.fetch(config_url, headers=self.headers)
                config_content = config_response.text

                # 查找配置中的播放器信息
                if 'danmu' in config_content:
                    self.log("播放器配置中包含弹幕相关信息")

            except Exception as e:
                self.log(f"获取播放器配置失败: {e}")

            # 如果都没找到直播流，返回播放页面让系统解析
            self.log(f"未找到直播流地址，返回播放页面: {play_url}")
            return {
                "parse": 1,
                "url": play_url,
                "header": self.headers
            }

        except Exception as e:
            self.log(f"获取播放地址失败: {e}")
            return {
                "parse": 1,
                "url": f"{self.host}{id}",
                "header": self.headers
            }

    def getpq(self, data):
        """获取pyquery对象"""
        try:
            return pq(data)
        except Exception as e:
            self.log(f"解析HTML失败: {e}")
            return pq("")
