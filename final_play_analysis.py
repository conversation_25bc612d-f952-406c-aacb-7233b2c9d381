# -*- coding: utf-8 -*-
# 最终播放机制分析 - 寻找真实播放地址
import requests
import re
import json
import time
from pyquery import PyQuery as pq

def final_analysis():
    """最终分析NO视频的播放机制"""
    
    print("=" * 80)
    print("最终播放机制分析")
    print("=" * 80)
    
    # 测试URL
    detail_url = 'https://www.novipnoad.net/tv/western/150717.html'
    vid = 'ftn-1752837192'
    vid_url = f'{detail_url}?vid={vid}'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': detail_url,
    }
    
    try:
        # 1. 获取带vid的页面
        print(f"\n1. 获取带vid页面: {vid_url}")
        response = requests.get(vid_url, headers=headers, timeout=15)
        content = response.text
        data = pq(content)
        
        # 2. 分析页面是否有变化
        print(f"\n2. 分析页面变化...")
        
        # 检查播放器容器是否有内容
        player_embed = data('#player-embed')
        if len(player_embed) > 0:
            player_html = str(player_embed)
            print(f"播放器容器内容: {player_html[:500]}...")
            
            # 查找iframe
            iframes = player_embed('iframe')
            if len(iframes) > 0:
                for i, iframe in enumerate(iframes.items()):
                    src = iframe.attr('src')
                    print(f"  iframe {i+1}: {src}")
        
        # 3. 查找所有可能的播放相关URL
        print(f"\n3. 查找播放相关URL...")
        
        # 更全面的URL模式
        url_patterns = [
            r'(https?://[^"\'>\s]*(?:m3u8|mp4|flv|avi|mkv)[^"\'>\s]*)',
            r'(https?://[^"\'>\s]*(?:video|stream|play|player)[^"\'>\s]*)',
            r'(https?://[^"\'>\s]*\.(?:m3u8|mp4|flv|avi|mkv))',
            r'src=["\']([^"\']+)["\']',
            r'url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'link["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match[0] else (match[1] if len(match) > 1 else '')
                if match and 'http' in match:
                    found_urls.add(match)
        
        print(f"找到的URL:")
        for url in found_urls:
            print(f"  🔗 {url}")
        
        # 4. 分析JavaScript执行
        print(f"\n4. 分析JavaScript执行...")
        
        # 查找可能动态加载播放器的JavaScript
        scripts = data('script')
        for i, script in enumerate(scripts.items()):
            script_content = script.text()
            if script_content and ('player' in script_content.lower() or 'video' in script_content.lower()):
                print(f"\n播放相关脚本 {i+1}:")
                print(f"长度: {len(script_content)} 字符")
                
                # 查找函数调用
                function_calls = re.findall(r'(\w+)\s*\([^)]*\)', script_content)
                if function_calls:
                    print(f"函数调用: {function_calls[:10]}")  # 只显示前10个
                
                # 查找变量赋值
                assignments = re.findall(r'(\w+)\s*=\s*["\']([^"\']+)["\']', script_content)
                if assignments:
                    print(f"变量赋值: {assignments[:5]}")  # 只显示前5个
        
        # 5. 尝试模拟浏览器行为
        print(f"\n5. 模拟浏览器行为...")
        
        # 模拟点击播放按钮后可能的AJAX请求
        ajax_headers = headers.copy()
        ajax_headers.update({
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        })
        
        # 提取pkey
        pkey_match = re.search(r'window\.playInfo\s*=\s*\{\s*pkey\s*:\s*["\']([^"\']+)["\']', content)
        if pkey_match:
            pkey = pkey_match.group(1)
            print(f"使用pkey: {pkey}")
            
            # 尝试不同的API调用方式
            api_calls = [
                # 可能的播放API
                {
                    'url': 'https://www.novipnoad.net/lib/ajax.php',
                    'data': {'action': 'play', 'vid': vid, 'pkey': pkey}
                },
                {
                    'url': 'https://www.novipnoad.net/api/play.php',
                    'data': {'vid': vid, 'key': pkey}
                },
                {
                    'url': 'https://www.novipnoad.net/player/api.php',
                    'data': {'id': vid, 'token': pkey}
                }
            ]
            
            for call in api_calls:
                try:
                    print(f"\n尝试API: {call['url']}")
                    response = requests.post(call['url'], data=call['data'], headers=ajax_headers, timeout=10)
                    print(f"状态码: {response.status_code}")
                    print(f"响应: {response.text[:200]}...")
                    
                    if response.status_code == 200 and response.text.strip() and response.text.strip() != "0":
                        try:
                            json_data = json.loads(response.text)
                            print(f"JSON数据: {json_data}")
                        except:
                            print(f"非JSON响应: {response.text}")
                except Exception as e:
                    print(f"请求失败: {e}")
        
        # 6. 检查是否需要等待页面加载
        print(f"\n6. 检查动态内容...")
        
        # 等待一段时间后重新检查播放器容器
        print("等待3秒后重新检查...")
        time.sleep(3)
        
        # 重新获取页面
        response2 = requests.get(vid_url, headers=headers, timeout=15)
        content2 = response2.text
        data2 = pq(content2)
        
        player_embed2 = data2('#player-embed')
        if len(player_embed2) > 0:
            player_html2 = str(player_embed2)
            if player_html2 != str(player_embed):
                print("播放器容器内容发生变化!")
                print(f"新内容: {player_html2[:500]}...")
            else:
                print("播放器容器内容未变化")
        
        # 7. 分析可能的解决方案
        print(f"\n7. 分析可能的解决方案...")
        
        print("基于分析，NO视频网站可能使用以下机制之一:")
        print("1. 需要特定的User-Agent或其他请求头")
        print("2. 使用JavaScript动态生成播放地址")
        print("3. 需要模拟完整的浏览器环境")
        print("4. 播放地址通过WebSocket或其他方式获取")
        print("5. 使用第三方播放器服务")
        
        # 8. 最终建议
        print(f"\n8. 最终建议...")
        print("对于PyramidStore插件，建议采用以下策略:")
        print("1. 返回带vid参数的详情页面URL，让系统进一步解析")
        print("2. 设置parse=1，让播放器尝试解析页面")
        print("3. 提供完整的请求头信息")
        print("4. 考虑使用浏览器自动化工具进行深度解析")
        
        print("\n" + "=" * 80)
        print("最终分析完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_analysis()
