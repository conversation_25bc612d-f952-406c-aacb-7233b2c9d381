#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
泥视频插件测试脚本
"""

import sys
sys.path.append('.')

from plugin.html.泥视频 import Spider

def test_spider():
    """测试泥视频插件的所有功能"""
    spider = Spider()
    print("=" * 60)
    print("泥视频插件功能测试")
    print("=" * 60)
    
    # 1. 测试首页内容
    print("\n1. 测试首页内容...")
    try:
        home_result = spider.homeContent({})
        videos = home_result.get('list', [])
        classes = home_result.get('class', [])
        
        print(f"   ✓ 首页获取成功")
        print(f"   ✓ 分类数量: {len(classes)}")
        print(f"   ✓ 视频数量: {len(videos)}")
        
        if classes:
            print("   ✓ 分类列表:")
            for cls in classes:
                print(f"      - {cls['type_name']} (ID: {cls['type_id']})")
        
        if videos:
            print(f"   ✓ 首个视频: {videos[0]['vod_name']} (ID: {videos[0]['vod_id']})")
            
    except Exception as e:
        print(f"   ✗ 首页测试失败: {e}")
    
    # 2. 测试分类内容
    print("\n2. 测试分类内容（电影）...")
    try:
        category_result = spider.categoryContent('1', '1', {}, {})
        videos = category_result.get('list', [])
        
        print(f"   ✓ 分类页获取成功")
        print(f"   ✓ 视频数量: {len(videos)}")
        print(f"   ✓ 当前页: {category_result.get('page', 0)}")
        
        if videos:
            print(f"   ✓ 首个视频: {videos[0]['vod_name']} (ID: {videos[0]['vod_id']})")
            
    except Exception as e:
        print(f"   ✗ 分类测试失败: {e}")
    
    # 3. 测试搜索功能
    print("\n3. 测试搜索功能...")
    try:
        search_result = spider.searchContent('凡人修仙传', False, '1')
        videos = search_result.get('list', [])
        
        print(f"   ✓ 搜索功能正常")
        print(f"   ✓ 搜索结果数量: {len(videos)}")
        
        if videos:
            print("   ✓ 搜索结果:")
            for i, video in enumerate(videos[:3]):  # 只显示前3个
                print(f"      {i+1}. {video['vod_name']} (ID: {video['vod_id']}) - {video['vod_remarks']}")
                
    except Exception as e:
        print(f"   ✗ 搜索测试失败: {e}")
    
    # 4. 测试详情页面
    print("\n4. 测试详情页面...")
    try:
        detail_result = spider.detailContent(['82063'])
        if detail_result.get('list'):
            video = detail_result['list'][0]
            
            print(f"   ✓ 详情页获取成功")
            print(f"   ✓ 标题: {video.get('vod_name', '')}")
            print(f"   ✓ 导演: {video.get('vod_director', '')}")
            print(f"   ✓ 主演: {video.get('vod_actor', '')[:50]}...")
            
            play_from = video.get('vod_play_from', '')
            play_url = video.get('vod_play_url', '')
            
            if play_from and play_url:
                sources = play_from.split('$$$')
                urls = play_url.split('$$$')
                print(f"   ✓ 播放源数量: {len(sources)}")
                
                if urls and urls[0]:
                    episodes = urls[0].split('#')
                    print(f"   ✓ 第一个播放源剧集数: {len(episodes)}")
                    if episodes:
                        print(f"   ✓ 首集: {episodes[0].split('$')[0] if '$' in episodes[0] else episodes[0]}")
            
            content = video.get('vod_content', '')
            if content:
                print(f"   ✓ 剧情简介: {content[:80]}...")
        else:
            print("   ✗ 详情页面无数据")
            
    except Exception as e:
        print(f"   ✗ 详情测试失败: {e}")
    
    # 5. 测试播放功能
    print("\n5. 测试播放功能...")
    try:
        play_result = spider.playerContent('', '/niplay/82063-1-1/', [])
        
        print(f"   ✓ 播放解析完成")
        print(f"   ✓ Parse模式: {play_result.get('parse', '')}")
        
        url = play_result.get('url', '')
        if url:
            print(f"   ✓ 播放地址: {url[:60]}...")
        
        header = play_result.get('header', {})
        if header and 'User-Agent' in header:
            print(f"   ✓ 请求头设置正确")
            
    except Exception as e:
        print(f"   ✗ 播放测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_spider()
