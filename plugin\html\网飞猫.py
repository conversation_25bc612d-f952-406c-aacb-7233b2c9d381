# -*- coding: utf-8 -*-
# 网飞猫插件 - 最新Netflix新剧_韩国电影免费在线观看
import re
import sys
import json
import time
import base64
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        pass

    def getName(self):
        return "网飞猫"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def action(self, action):
        pass

    def destroy(self):
        pass

    host = 'https://www.ncat22.com'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }

    def homeContent(self, filter):
        try:
            response = self.fetch(self.host, headers=self.headers)
            data = self.getpq(response.text)
            
            result = {}
            classes = []
            
            # 添加分类
            categories = [
                {'type_name': '电影', 'type_id': '1'},
                {'type_name': '连续剧', 'type_id': '2'},
                {'type_name': '动漫', 'type_id': '3'},
                {'type_name': '综艺纪录', 'type_id': '4'},
                {'type_name': '短剧', 'type_id': '6'}
            ]
            classes.extend(categories)
            
            # 获取首页推荐内容
            videos = []
            items = data('.module-item')
            
            for item in items.items():
                try:
                    # 提取详情链接
                    detail_link = item('a[href*="/detail/"]').attr('href')
                    if not detail_link:
                        continue
                    
                    # 提取视频ID
                    vod_id = re.search(r'/detail/(\d+)\.html', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)
                    
                    # 提取标题
                    title_elem = item('a[href*="/detail/"]')
                    vod_name = title_elem.text().strip()
                    # 清理标题中的多余文本
                    vod_name = re.sub(r'可可影视.*?com\s*', '', vod_name).strip()
                    if not vod_name:
                        continue
                    
                    # 提取图片
                    img_elem = item('img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = self.host + vod_pic
                    
                    # 提取备注信息
                    vod_remarks = ''
                    text_content = item.text()
                    # 提取状态信息（如HD国语、正片等）
                    status_match = re.search(r'(HD\w*|正片|更新\w*|完结)', text_content)
                    if status_match:
                        vod_remarks = status_match.group(1)
                    
                    # 年份暂时留空，在详情页获取
                    vod_year = ''
                    
                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析首页视频项失败: {e}")
                    continue
            
            result['class'] = classes
            result['list'] = videos
            return result
            
        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return {'class': [], 'list': []}

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        try:
            url = f"{self.host}/channel/{tid}.html"
            if int(pg) > 1:
                url = f"{self.host}/channel/{tid}-{pg}.html"
            
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            items = data('.module-item')
            
            for item in items.items():
                try:
                    # 提取详情链接
                    detail_link = item('a[href*="/detail/"]').attr('href')
                    if not detail_link:
                        continue
                    
                    # 提取视频ID
                    vod_id = re.search(r'/detail/(\d+)\.html', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)
                    
                    # 提取标题
                    title_elem = item('a[href*="/detail/"]')
                    vod_name = title_elem.text().strip()
                    # 清理标题中的多余文本
                    vod_name = re.sub(r'可可影视.*?com\s*', '', vod_name).strip()
                    if not vod_name:
                        continue
                    
                    # 提取图片
                    img_elem = item('img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = self.host + vod_pic
                    
                    # 提取备注信息
                    vod_remarks = ''
                    text_content = item.text()
                    # 提取状态信息（如HD国语、正片等）
                    status_match = re.search(r'(HD\w*|正片|更新\w*|完结)', text_content)
                    if status_match:
                        vod_remarks = status_match.group(1)
                    
                    # 年份暂时留空，在详情页获取
                    vod_year = ''
                    
                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析分类视频项失败: {e}")
                    continue
            
            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': 9999,  # 网飞猫没有明确的总页数，设置较大值
                'limit': 20,
                'total': 999999
            }
            return result
            
        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 0, 'limit': 20, 'total': 0}

    def detailContent(self, ids):
        try:
            vod_id = ids[0]
            url = f"{self.host}/detail/{vod_id}.html"
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)
            
            # 提取基本信息
            title = data('title').text()
            # 清理标题
            title = re.sub(r'-.*?网飞猫.*', '', title).strip()
            
            # 查找图片
            pic = ''
            img_elem = data('img')
            for img in img_elem.items():
                src = img.attr('data-original') or img.attr('src')
                if src and 'placeholder' not in src and 'logo' not in src:
                    pic = src if src.startswith('http') else self.host + src
                    break
            
            # 提取详细信息（这个网站的详情页结构比较简单）
            vod_year = ''
            vod_area = ''
            vod_lang = ''
            vod_director = ''
            vod_actor = ''
            type_name = ''
            vod_content = ''
            
            # 提取播放列表
            play_sources = []
            play_urls = []
            
            # 查找播放链接
            play_links = data('a[href*="/play/"]')
            if len(play_links) > 0:
                play_sources.append('默认播放源')
                episodes = []
                
                for i, link in enumerate(play_links.items()):
                    ep_url = link.attr('href')
                    ep_name = link.text().strip() or f'播放{i+1}'
                    if ep_url:
                        episodes.append(f"{ep_name}${ep_url}")
                
                play_urls.append('#'.join(episodes))
            
            vod = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': vod_year,
                'vod_area': vod_area,
                'vod_lang': vod_lang,
                'vod_director': vod_director,
                'vod_actor': vod_actor,
                'vod_content': vod_content,
                'vod_play_from': '$$$'.join(play_sources),
                'vod_play_url': '$$$'.join(play_urls)
            }
            
            return {'list': [vod]}
            
        except Exception as e:
            self.log(f"获取详情内容失败: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        try:
            # 网飞猫的搜索需要特殊的加密参数，暂时使用简单的base64编码
            # 实际的加密算法可能更复杂，这里先实现基本功能
            import time
            timestamp = str(int(time.time()))
            # 简单的token生成，实际可能需要更复杂的算法
            token = base64.b64encode(timestamp.encode()).decode()
            
            search_url = f"{self.host}/search?t={quote(token)}&k={quote(key)}"
            response = self.fetch(search_url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            # 搜索结果可能使用不同的结构
            items = data('.module-item, a[href*="/detail/"]')
            
            for item in items.items():
                try:
                    # 如果是.module-item，查找其中的详情链接
                    if item.hasClass('module-item'):
                        detail_link = item('a[href*="/detail/"]').attr('href')
                        title_elem = item('a[href*="/detail/"]')
                    else:
                        # 如果直接是详情链接
                        detail_link = item.attr('href')
                        title_elem = item
                    
                    if not detail_link:
                        continue
                    
                    # 提取视频ID
                    vod_id = re.search(r'/detail/(\d+)\.html', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)
                    
                    # 提取标题
                    vod_name = title_elem.text().strip()
                    # 清理标题中的多余文本
                    vod_name = re.sub(r'可可影视.*?com\s*', '', vod_name).strip()
                    if not vod_name:
                        continue
                    
                    # 提取图片
                    img_elem = item('img')
                    vod_pic = ''
                    if len(img_elem) > 0:
                        vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                        if vod_pic and not vod_pic.startswith('http'):
                            vod_pic = self.host + vod_pic
                    
                    # 提取备注信息
                    vod_remarks = ''
                    text_content = item.text()
                    status_match = re.search(r'(HD\w*|正片|更新\w*|完结)', text_content)
                    if status_match:
                        vod_remarks = status_match.group(1)
                    
                    vod_year = ''
                    
                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析搜索结果项失败: {e}")
                    continue
            
            return {'list': videos, 'page': int(pg)}
            
        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {'list': [], 'page': int(pg)}

    def playerContent(self, flag, id, vipFlags):
        try:
            play_url = f"{self.host}{id}"
            response = self.fetch(play_url, headers=self.headers)
            content = response.text
            
            # 尝试从页面中提取播放地址
            # 方法1: 查找m3u8链接
            m3u8_pattern = r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)'
            m3u8_match = re.search(m3u8_pattern, content)
            
            if m3u8_match:
                video_url = m3u8_match.group(1)
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }
            
            # 方法2: 查找mp4链接
            mp4_pattern = r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)'
            mp4_match = re.search(mp4_pattern, content)
            
            if mp4_match:
                video_url = mp4_match.group(1)
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }
            
            # 方法3: 查找iframe中的播放器
            iframe_pattern = r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>'
            iframe_match = re.search(iframe_pattern, content)
            
            if iframe_match:
                iframe_url = iframe_match.group(1)
                if not iframe_url.startswith('http'):
                    iframe_url = self.host + iframe_url
                
                # 获取iframe内容
                iframe_response = self.fetch(iframe_url, headers=self.headers)
                iframe_content = iframe_response.text
                
                # 在iframe中查找播放地址
                m3u8_match = re.search(m3u8_pattern, iframe_content)
                if m3u8_match:
                    video_url = m3u8_match.group(1)
                    return {
                        "parse": 0,
                        "url": video_url,
                        "header": self.headers
                    }
                
                mp4_match = re.search(mp4_pattern, iframe_content)
                if mp4_match:
                    video_url = mp4_match.group(1)
                    return {
                        "parse": 0,
                        "url": video_url,
                        "header": self.headers
                    }
            
            # 如果都没找到，返回原始播放页面让系统解析
            return {
                "parse": 1,
                "url": play_url,
                "header": self.headers
            }
            
        except Exception as e:
            self.log(f"获取播放地址失败: {e}")
            return {
                "parse": 1,
                "url": f"{self.host}{id}",
                "header": self.headers
            }

    def getpq(self, data):
        """获取pyquery对象"""
        try:
            return pq(data)
        except Exception as e:
            self.log(f"解析HTML失败: {e}")
            return pq("")
