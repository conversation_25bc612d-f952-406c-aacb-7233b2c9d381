{
    "spider": "./xm.jar;md5;349A237778A3CE50218E6C8D3B3E474D",
    "sites": [
        {
            "key": "xldrpy_js_豆瓣",
            "name": "豆瓣[js]",
            "type": 3,
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/drpy.js"
        },
        {
            "key": "AList",
            "name": "网盘|Alist[jar]",
            "type": 3,
            "api": "csp_AList",
            "searchable": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./alistjar.json"
        },
        {
            "key": "虾仁 66",
            "name": "Emby 4K",
            "type": 3,
            "api": "./py/emby_proxy.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "timeout": 60,
            "ext": {
                "server": "https://shsiis.cloudfisher.cc:443",
                "username": "j<PERSON><PERSON>",
                "password": "a4AXEtxVIL",
                "thread": 0,
                "proxy": ""
            },
            "changeable": 1
        },
        {
            "key": "网盘配置",
            "name": "网盘及彈幕配置",
            "type": 3,
            "api": "csp_Config",
            "searchable": 0,
            "changeable": 0,
            "ext": "./lib/tokenm.json",
            "style": {
                "type": "rect",
                "ratio": 1.5
            }
        },
        {
            "key": "xlcsp_XYQHiker_4KVM",
            "name": "4KVM影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/4KVM.json"
        },
        {   
            "key": "py_光速",
            "name": "光速｜py",
            "type": 3,
            "api": "./py/py_光速.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {   
            "key": "py_明月",
            "name": "明月影视｜py",
            "type": 3,
            "api": "./py/cnotv.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_kimivodnk",
            "name": "bttwoo｜py",
            "type": 3,
            "api": "./py/bttwoo.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速CliCli动漫APP",
            "name": "CliCli动漫APP｜py",
            "type": 3,
            "api": "./py/CliCli动漫APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
            {
            "key": "py_光速CliClihdmoliAPP",
            "name": "hbottv｜py",
            "type": 3,
            "api": "./py/HBOTTV.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速MiFunAPP",
            "name": "MiFunAPP｜py",
            "type": 3,
            "api": "./py/MiFunAPP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速统一影视MiFunAPP",
            "name": "统一影视｜py",
            "type": 3,
            "api": "./py/统一影视.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速统一4K影视MiFunAPP",
            "name": "4KVM影视｜py",
            "type": 3,
            "api": "./py/kvm.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速MiFunAPP播剧网影视.py",
            "name": "播剧网｜py",
            "type": 3,
            "api": "./py/播剧网.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速MiFunAPP4KAV",
            "name": "4KA｜py",
            "type": 3,
            "api": "./py/4KAV.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光APP4KAV柯南影视",
            "name": "柯南影视｜py",
            "type": 3,
            "api": "./py/柯南影视.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光APP厂长资源柯南影视",
            "name": "厂长资源｜py",
            "type": 3,
            "api": "./py/厂长资源.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光APP4KAV海马影视",
            "name": "海马影视APP｜py",
            "type": 3,
            "api": "./py/海马影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_零度影视APP",
            "name": "零度影视APP｜py",
            "type": 3,
            "api": "./py/零度影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云端APP",
            "name": "云端APP｜py",
            "type": 3,
            "api": "./py/云端APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云端APP奇迹APP",
            "name": "奇迹APP｜py",
            "type": 3,
            "api": "./py/奇迹APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云端APP偷乐短剧",
            "name": "偷乐短剧｜py",
            "type": 3,
            "api": "./py/偷乐短剧.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云端APP剧粑粑",
            "name": "剧粑粑｜py",
            "type": 3,
            "api": "./py/剧粑粑.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云速影视APP",
            "name": "云速影视APP｜py",
            "type": 3,
            "api": "./py/云速影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速云速影视热播APPAPP",
            "name": "热播APP｜py",
            "type": 3,
            "api": "./py/热播APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速恋鱼影视APP",
            "name": "恋鱼影视APP｜py",
            "type": 3,
            "api": "./py/恋鱼影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速恋鱼影视旺旺APP",
            "name": "旺旺｜py",
            "type": 3,
            "api": "./py/旺旺.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速魔方影视APP",
            "name": "魔方影视APP｜py",
            "type": 3,
            "api": "./py/魔方影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "LREEOK",
            "name": "LREEOK｜py",
            "type": 3,
            "api": "./py/LREEOK.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "LREEOK骚火电影",
            "name": "骚火电影｜py",
            "type": 3,
            "api": "./py/骚火电影.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "LREEOK小红影视",
            "name": "小红影视｜py",
            "type": 3,
            "api": "./py/小红影视.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "LREEOK嗷呜动漫",
            "name": "嗷呜动漫｜py",
            "type": 3,
            "api": "./py/嗷呜动漫.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速三号动漫APP",
            "name": "三号动漫APP｜py",
            "type": 3,
            "api": "./py/三号动漫APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速悠悠APP",
            "name": "悠悠APP｜py",
            "type": 3,
            "api": "./py/悠悠APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速悠悠国外剧APPAPP",
            "name": "国外剧APP｜py",
            "type": 3,
            "api": "./py/国外剧APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "hipy_js_美柏",
            "name": "美柏┃视频",
            "type": 3,
            "api": "./py/pymp.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "py_光速爱瓜TVAPP",
            "name": "爱瓜TVAPP｜py",
            "type": 3,
            "api": "./py/爱瓜TVAPP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_金牌",
            "name": "金牌",
            "type": 3,
            "api": "./py/金牌.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": {
                "site": "https://www.jiabaide.cn,https://cqzuoer.com"
            }
        },
        {
            "key": "py_光速app",
            "name": "光速App｜py",
            "type": 3,
            "api": "./py/光速APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_若惜追剧APP",
            "name": "若惜追剧APP｜py",
            "type": 3,
            "api": "./py/若惜追剧APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_光速app哇哇APP",
            "name": "哇哇APP｜py",
            "type": 3,
            "api": "./py/哇哇APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_红果网页",
            "name": "红果网页｜py",
            "type": 3,
            "api": "./py/红果网页.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_边缘",
            "name": "边缘｜py",
            "type": 3,
            "api": "./py/边缘影视APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
    
        {
            "key": "py_hitv",
            "name": "hitv｜py",
            "type": 3,
            "api": "./py/py_hitv.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_小苹果APP",
            "name": "小苹果APP｜py",
            "type": 3,
            "api": "./py/小苹果APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_视觉APP",
            "name": "视觉APP｜py",
            "type": 3,
            "api": "./py/视觉APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_胖虎APP",
            "name": "胖虎APP｜py",
            "type": 3,
            "api": "./py/胖虎APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_美帕APP",
            "name": "美帕APP｜py",
            "type": 3,
            "api": "./py/美帕APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_绝对影视",
            "name": "绝对影视｜py",
            "type": 3,
            "api": "./py/绝对影视.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "py_火车太顺APP",
            "name": "火车太顺APP｜py",
            "type": 3,
            "api": "./py/火车太顺APP.py",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "filterable": 1,
            "timeout": 60
        },
        {
            "key": "WebDAV",
            "name": "WebDAV[jar]",
            "type": 3,
            "api": "csp_WebDAV",
            "searchable": 1,
            "filterable": 1,
            "changeable": 1,
            "timeout": 60,
            "playerType": 2,
            "ext": "./lib/webdav.json"
        },
        {
            "key": "xm荐片",
            "name": "➖荐片┃磁力",
            "quickSearch": 0,
            "searchable": 1,
            "type": 3,
            "api": "csp_Jianpian",
            "style": {
                "type": "rect",
                "ratio": 1.333
            },
            "ext": ""
        },
        {
            "key": "xm娱乐",
            "name": "🎮游戏┃娱乐",
            "quickSearch": 0,
            "searchable": 1,
            "type": 3,
            "api": "./lib/apiv323LIVES.py",
            "style": {
                "type": "rect",
                "ratio": 1.333
            },
            "ext": ""
        },
        {
            "key": "xm88看球",
            "name": "🏀看球┃直播",
            "type": 3,
            "api": "csp_Kanqiu",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 0,
            "changeable": 0
        },
        {
            "key": "xm天天",
            "name": "💯天天┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "csp_AppRJ",
            "ext": "vxw35/hHSj07Q+maxQzOVMq1rjRCOTXpUCx8iKu5jIg="
        },
        {
            "key": "xmcsp_nongmin",
            "name": "💯农民┃极速",
            "type": 3,
            "api": "csp_Wwys",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "https://www.wwgz.cn"
        },
        {
            "key": "xm斗量",
            "name": "🎃南瓜┃APP",
            "type": 3,
            "jar": "./lib/071.png;md5;9cf0a6c1f62ad83e9dccc2b7dd683a10",
            "api": "csp_NanGua",
            "quickSearch": 0,
            "playerType": 2,
            "ext": "7lj763gg0939795i017ii486k512jkihhilg9g0h96j865740113"
        },
        {
            "key": "xm热热",
            "name": "🔥热热┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_AppRJ",
            "ext": "jsSMEuhTZIAHjnUoLBzKdlRu5exzno6M4efF8LzwjWM="
        },
        {
            "key": "xm云速",
            "name": "☁️云速┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_AppGet",
            "ext": "rP+4azM8YFgp3eAhyeALtUUEFpbkqmzGj2V5VZryxRbpkSjlsHbarRCZPOInvId4s3WK9rZ2YJsDy8NvMqQKQXowVmh33j+qD55VxmvMzzfiJ2fHkCRZg4+8NFWs+b1X"
        },
        {
            "key": "xm幕启",
            "name": "🌉幕启┃影视",
            "type": 3,
            "api": "csp_AppSy",
            "quickSearch": 0,
            "ext": "J7WBJTWvt49Rro0WaKnCpcwiAwtiA57atMnVBDAP3YRNynoyyCyLIvc6/nXpdo/2nerMlKFDJbIGRKuy0qJdB6Qkf23z5CWmuH7UTrHERqI="
        },
        {
            "key": "xm追忆",
            "name": "🍯追忆┃影视",
            "type": 3,
            "api": "csp_AppSy",
            "quickSearch": 0,
            "ext": "9rfWRaAx60J+W5F+j6agt6AHU3NkRl7m8WqfWIBU/MebSZgzY2wnsXwmAIMEYVqMzsnqr46bOn1q4JV/UJAKUVF6NUo2Xg8qcHgXrA63RWM="
        },
        {
            "key": "xm麻花",
            "name": "🌸麻花┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "rP+4azM8YFgp3eAhyeALtUUEFpbkqmzGj2V5VZryxRbpkSjlsHbarRCZPOInvId4s3WK9rZ2YJsDy8NvMqQKQXowVmh33j+qD55VxmvMzzesNDM87sUul1Ii8NbP2XB5"
        },
        {
            "key": "xmQD4K",
            "name": "🐷猪猪┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "./lib/apiv555AppV2.py",
            "playerType": 2,
            "ext": {
                "api": "http://dmmax.juxiafan.com/icciu_api.php/v1.vod",
                "datasignkey": "6QQNUsP3PkD2ajJCPCY8",
                "apisignkey": "lvdoutv-1.0.0"
            }
        },
        {
            "key": "xm咖啡",
            "name": "☕咖啡┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "qoNcYCY4SGJFWERC5ewNdoRMWRiiOH3dKBG5+Zte38B0REfz5STn0coM8gQ4pJtU"
        },
        {
            "key": "xm大豆",
            "name": "✅大豆┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "Qrf5S6Si5oF7dQyuv+Srh3uh0lT3z1Y7u59ip9hRVeUKFHSUUbLyGMREENFjE1N9FXjZ6Z7tiLWs6P15Ol/g5Po80zDNWJPEEoj/nv3Yelo="
        },
        {
            "key": "xmDAY",
            "name": "🧵UPUP┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "./lib/apiv745tt.py"
        },
        {
            "key": "xm步步",
            "name": "👟步步┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "./lib/apiv763drpy2.min.js",
            "ext": "./lib/测试.js"
        },
        {
            "key": "xm牛牛",
            "name": "🍁牛牛┃影视",
            "type": 3,
            "quickSearch": 0,
            "searchable": 1,
            "api": "./lib/apiv785mioaying.py"
        },
        {
            "key": "xmcsp_AppXY",
            "name": "🐸短剧┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_AppXY",
            "ext": "https://xvapp.xingya.com.cn"
        },
        {
            "key": "xmcsp_baibai",
            "name": "💯白白┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_SuBaiBai",
            "ext": "https://www.subaibai.com"
        },
        {
            "key": "xmcsp_LiteApple",
            "name": "🍎苹果┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_LiteApple"
        },
        {
            "key": "xmcsp_Gz360",
            "name": "🍉瓜子┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_Gz360"
        },
        {
            "key": "xmcsp_Jpys",
            "name": "🥇金牌┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "csp_Jpys"
        },
        {
            "key": "xm聚吧",
            "name": "🐓剧霸┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 0,
            "api": "./lib/apiv901jubaba.py",
            "ext": ""
        },
        {
            "key": "xldrpy_js_奇珍异兽",
            "name": "奇珍异兽[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/奇珍异兽.js"
        },
        {
            "key": "xldrpy_js_优酷",
            "name": "优酷[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/优酷.js"
        },
        {
            "key": "xldrpy_js_腾云驾雾",
            "name": "腾云驾雾[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/腾云驾雾.js"
        },
        {
            "key": "xldrpy_js_百忙无果",
            "name": "百忙无果[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/百忙无果.js"
        },
        {
            "key": "xlcsp_New6v",
            "name": "🧲新6V",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_New6v",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.xb6v.com"
        },
        {
            "key": "xlcsp_DyGod",
            "name": "🧲电影天堂",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_DyGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "xlcsp_QnMp4",
            "name": "🧲七妹",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_QnMp4",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "xlcsp_SeedHub",
            "name": "🧲SeedHub",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_SeedHub",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.seedhub.cc"
        },
        //{"key":"csp_KubaCL","name":"🧲酷吧电影","type":3,"api":"csp_KubaCL","searchable":1,"quickSearch":1,"filterable":1,"ext":"https://www.kubady2.com"},
        {
            "key": "xlcsp_MeijuTT",
            "name": "🧲美剧天堂",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_MeijuTT",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.meijutt.net"
        },
        //{"key":"csp_MeijuMi","name":"🧲美剧迷","type":3,"api":"csp_MeijuMi","searchable":1,"quickSearch":1,"filterable":1},
        {
            "key": "xlcsp_BLSGod",
            "name": "🧲80S影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_BLSGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "xlcsp_Xunlei8",
            "name": "🧲迅雷吧",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Xunlei8",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        //jar内置爬虫规则区
        {
            "key": "xl360_spider",
            "name": "🐞360影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_SP360",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1
        },
        {
            "key": "xlcsp_Kuaikan",
            "name": "💡快看影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Kuaikan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "xlcsp_LiteApple",
            "name": "🐞小苹果影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_LiteApple",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1
        },
        //{"key":"csp_Czsapp","name":"🐞厂长资源","type":3,"api":"csp_Czsapp","playerType":2,"searchable":1,"quickSearch":1,"filterable":1,"ext":"https://www.czzy77.com"},
        {
            "key": "xlcsp_Bdys",
            "name": "🐞哔嘀影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bdys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://v.xlys.ltd.ua"
        },
        {
            "key": "xlcsp_XYQHiker_修罗影视",
            "name": "🧲修罗影视(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/哔嘀影视.json"
        },
        {
            "key": "xlcsp_Ddys",
            "name": "🐞低端影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Ddys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        //{"key":"csp_Kunyu77","name":"🐞77影视","type":3,"api":"csp_Kunyu77","searchable":1,"quickSearch":1,"filterable":1},
        {
            "key": "xlcsp_JianPian",
            "name": "🔨荐片",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_JianPian",
            "playerType": 1,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "xlcsp_Ikanbot",
            "name": "👾Ikanbot",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Ikanbot",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://v.aikanbot.com"
        },
        //B站系列
        {
            "key": "xlcsp_Bili幼儿",
            "name": "🐞哔哩幼儿",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/幼儿乐园.json"
        },
        {
            "key": "xlcsp_Bili少儿",
            "name": "🐞哔哩少儿",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/少儿教育.json"
        },
        {
            "key": "xlcsp_Bili小学",
            "name": "🐞哔哩小学",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/小学课堂.json"
        },
        {
            "key": "xlcsp_Bili初中",
            "name": "🐞哔哩初中",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/初中课堂.json"
        },
        {
            "key": "xlcsp_Bili高中",
            "name": "🐞哔哩高中",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/高中课堂.json"
        },
        {
            "key": "xlJS哔哩直播",
            "name": "哔哩直播[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/哔哩直播.js"
        },
        {
            "key": "xlcsp_XYQHiker_戏曲多多",
            "name": "🎻戏曲多多",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/戏曲多多.json"
        },
        //{"key": "xlcsp_XYQHiker_酷奇MV","name":"🎤酷奇MV(XYQH)","type":3,"api":"csp_XYQHiker","searchable":0,"quickSearch":0,"filterable":1,"ext":"./lib/酷奇MV.json"},
        {
            "key": "xlcsp_XYQHiker_短剧屋",
            "name": "🎬短剧屋",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/短剧屋.json"
        },
        {
            "key": "xlcsp_XYQHiker_兔小贝",
            "name": "🐰兔小贝(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/兔小贝.json"
        },
        {
            "key": "xlcsp_XYQHiker_兔小贝2",
            "name": "🐰兔小贝2(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 0,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/兔小贝2.json"
        },
        {
            "key": "xlcsp_XYQHiker_播视童趣",
            "name": "👶🏻播视童趣(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/播视童趣.json"
        },
        {
            "key": "xlcsp_XYQHiker_风车动漫",
            "name": "🌪风车动漫(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/风车动漫.json"
        },
        {
            "key": "xlcsp_XYQHiker_樱花动漫",
            "name": "🌸樱花动漫(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/樱花动漫.json"
        },
        {
            "key": "xlcsp_XYQHiker_去看吧动漫",
            "name": "🔭去看吧动漫(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/去看吧动漫.json"
        },
        {
            "key": "xlcsp_XYQHiker_嗷呜动漫",
            "name": "🙀嗷呜动漫(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/嗷呜动漫.json"
        },
        {
            "key": "xlcsp_XYQHiker_动漫巴士",
            "name": "🚌动漫巴士(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/动漫巴士.json"
        },
        //XYQ规则区
        {
            "key": "xlcsp_XYQHiker_农民影视",
            "name": "🧑🏻‍农民影视",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/农民影视.json"
        },
        //{"key": "xlcsp_XYQHiker_七新影视","name":"🌟七新影视(XYQH)","type":3,"api":"csp_XYQHiker","searchable":1,"quickSearch":1,"filterable":1,"ext":"./lib/七新影视.json"},
        {
            "key": "xlcsp_XYQHiker_剧圈圈",
            "name": "🌟剧圈圈(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/剧圈圈.json",
            "click": "document.getElementById('playleft').children[0].contentWindow.document.getElementById('start').click()"
        },
        {
            "key": "xlcsp_XYQHiker_骚火电影VIP",
            "name": "🔥骚火电影VIP(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/骚火电影VIP.json"
        },
        {
            "key": "xlcsp_XYQHiker_电影盒子",
            "name": "📦电影盒子",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/电影盒子.json"
        },
        {
            "key": "xlcsp_XYQHiker_星辰影院",
            "name": "⭐️星辰影院(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/星辰影院.json"
        },
        {
            "key": "xlcsp_XYQHiker_可可影视",
            "name": "☕️可可影视(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/可可影视.json"
        },
        {
            "key": "xlcsp_XYQHiker_爱看影视",
            "name": "👀爱看影视(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/爱看影视.json"
        },
        {
            "key": "xlcsp_XYQHiker_看一看影视",
            "name": "🔍看一看影视(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/看一看影视.json"
        },
        //{"key": "xlcsp_XYQHiker_九八剧","name":"🍷九八剧(XYQH)","type":3,"api":"csp_XYQHiker","searchable":1,"quickSearch":1,"filterable":1,"ext":"./lib/九八剧.json"},
        {
            "key": "xlcsp_XYQHiker_瓜子影院",
            "name": "瓜子影院(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/瓜子影视.json"
        },
        {
            "key": "xlcsp_XYQHiker_八号影视",
            "name": "8️⃣八号影视(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/八号影视.json"
        },
        {
            "key": "xlcsp_XYQHiker_来看点播",
            "name": "⛅️来看点播(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./lib/来看点播.json"
        },
        {
            "key": "xldrpy_js_金牌",
            "name": "金牌影视[js]",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/金牌影视.js"
        },
        {
            "key": "xl柚子资源",
            "name": "🍊柚子资源",
            "type": 0,
            "api": "https://api.yzzy-api.com/inc/api.php",
            "searchable": 1,
            "quickSearch": 1,
            "categories": [
                "动作片",
                "喜剧片",
                "爱情片",
                "科幻片",
                "恐怖片",
                "剧情片",
                "战争片",
                "国产剧",
                "台湾剧",
                "韩国剧",
                "欧美剧",
                "香港剧",
                "泰国剧",
                "日本剧",
                "福利",
                "记录片",
                "动画片",
                "海外剧",
                "倫理片",
                "大陆综艺",
                "港台综艺",
                "日韩综艺",
                "欧美综艺",
                "国产动漫",
                "日韩动漫",
                "欧美动漫",
                "港台动漫",
                "海外动漫",
                "搞笑",
                "音乐",
                "影视",
                "汽车",
                "短剧大全",
                "预告片",
                "预告片",
                "体育"
            ]
        },

        {
            "key": "xlDRJS_虎牙",
            "name": "虎牙直播(JS)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "./lib/apiv127drpy2.min.js",
            "ext": "./lib/虎牙直播.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1
        },
        {
            "key": "xlcsp_XYQHiker_虎牙直播",
            "name": "🐯虎牙直播(XYQH)",
            "type": 3,
            "jar": "./xl.jar;md5;7F11D950239D38C975561A0A673E78E5",
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./lib/虎牙直播.json"
        },
        {
            "key": "qj天天",
            "name": "天天┃高峰期用不了就点我换源或者搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_TTian",
            "playerType": 2,
            "ext": "7lj763gg09397919456493i0h44j8681highi4"
        },
        {
            "key": "qj南瓜",
            "name": "南瓜┃秒播APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_NanGua",
            "playerType": 2,
            "ext": "7lj763gg0939795i017ii486k512jkihhilg9g0h96j865740113"
        },
        {
            "key": "qj拾光",
            "name": "拾光┃秒播APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Hmys",
            "playerType": 1,
            "ext": "https://dy.jmzp.net.cn"
        },
        {
            "key": "qjcsp_xlys",
            "name": "修罗┃无搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Bdys01",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "7lj763gg402i7942476492jlg94li29kk6gi8448ij"
        },
        {
            "key": "qj热播",
            "name": "热播┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_TTian",
            "playerType": 2,
            "ext": "7lj763gg0939791h1l3888jig44gi291li"
        },
        {
            "key": "qj追剧",
            "name": "追剧┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_TTian",
            "playerType": 2,
            "ext": "7lj763gg0939791h1l2681i6g94li291li"
        },
        {
            "key": "qj剧评",
            "name": "剧评┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Jpys",
            "playerType": 1,
            "ext": "7lj763gg402i7942463j9j9jh84l8798l8gli652828g332i"
        },
        {
            "key": "qj巧技二",
            "name": "巧技┃仅搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_qiao2",
            "playerType": 2,
            "ext": "7lj763gg402i79425739i7jghj118797l4hj840gi18633331l4708g2h7145403549g44l8ii56i187681hkjj3hhgh1ih3l32j250lk1k786lj20j468hk3hli4l46gig4i3g7g2722328j0136h01i7g5183k22k7gg3i72hk81gl8k9839kl7i0707"
        },
        {
            "key": "qj巧技三",
            "name": "巧技三┃QD4K",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_AppYsV2",
            "playerType": 2,
            "ext": "7lj763gg09397907492i87j2g8128687kgg1ih5hij8772225j4i54l2k91k151k138612h68842ji"
        },
        {
            "key": "qjyunyun",
            "name": "云云┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Qiji",
            "playerType": 2,
            "ext": "7lj763gg402i79425h2384j4g949j899hll9990i9kjl6l740h1342hjlg1848401g8610h9995i998j351gl38kklkj1hkhll757010k7hji6h377ih37k060g35161jhl3i5h0l2702g7299452l4297k5697g5390lk6935k2i9g48l8j2kh27l1g014k7j9475g8"
        },
        {
            "key": "qjlanyingys",
            "name": "橘子4K┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Qiji",
            "playerType": 2,
            "ext": "7lj763gg0939795i0678i481k40hi2i3ghlg840i9lj166700g1449g3k5244k2017h35698h4739ih31117kigil3k02hl2jk6i5155ih9kkgl311gl37g240g85111ggg2"
        },
        {
            "key": "qjkafeiys",
            "name": "狂风┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Qiji",
            "playerType": 2,
            "ext": "7lj763gg402i79425k2785j5g552i29kljg4855ki38l3g24570l0lkhkj0h4g191k8449gk8j5h9i942g0gh891k2l104l6k765664gljg7i2l336942gl67jh9405ighg592"
        },
        {
            "key": "qjhuomaoys",
            "name": "火猫┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg402i7942523l84j0h849j4jiljggj913il87712l5j530llhh4431503029k48h68l579g873712g887h8ll17kghl302610l0ggjlk00ij31ijg409l666gg9i7l38jji5i0l2183686g74hhj87g4j58k2ig5l08iik581ji9778l038"
        },
        {
            "key": "qjyizys",
            "name": "驿站┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Muou",
            "playerType": 2,
            "ext": "7lj763gg402i7942403h83i2h945858hljhji148i18k2837535112l2qiaojik9075l17028i49g192419i8g3245h3j1l9gi02h6k7732650h3h09jkg759j65hj39l50347k3gj97l4g12l7h6418h6k9j04l26i1glgj3631973hh280lkihjh"
        },
        {
            "key": "qj主角",
            "name": "主角┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Muou",
            "playerType": 2,
            "ext": "7lj763gg402i794247258k9jh6598585l3g6ij13il8g20g9qiaoji8j9i9k1g3k90h7i507i213k5j602"
        },
        {
            "key": "qj柚子",
            "name": "柚子┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg093979590864j880k512jji3g6k69h0496ji693j43161il9gj11020i09i55hhh8h049j8i270ikg84lig81khkl2722758k8g19jl22k996gg23l"
        },
        {
            "key": "qj永夜",
            "name": "永夜┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg402i7942522l99jhh90ljii5g1l09i13i39g2l6i514l56l1li1k5107159113h6945h8g91351kgkijl5l315g8l373614ik8lijlk4329907gg37867541j6k3kjl0k15473488h47206lihgl635k739jkk4175l1kjljh59l2ihj394j43"
        },
        {
            "key": "qj趣看",
            "name": "趣看┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Muou",
            "playerType": 2,
            "ext": "7lj763gg402i7942463j9j9jg1499j87l9g9i413il8gqiaoji20g7g4919j3g019296lk9351hh3k5gg38lj0754il3jg"
        },
        {
            "key": "qj星河",
            "name": "星河┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg402i794247239g9jk10kjl82l3g6845ki2i66j7906194lg6g8460h5k548i51l8805497807509g597kgk419l4l461675gl5g5ikkk329i28lg60h15541k9l3i3h1l266357j8j5625409hl8543522k5"
        },
        {
            "key": "qj时常",
            "name": "时常┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Qiji",
            "playerType": 2,
            "ext": "7lj763gg402i7942423k8hi1h1498h87k1hi9h0l9ljj69771i4k08k0h4145543139753h0825g9i91751ih18gklk916k4k32k7751k9lj9gh2329j29kl7ih94i52lhk2i5h7k563732gii1669038kk45i6760h0kl6932l5jgljj7j87jl43k03423j31"
        },
        {
            "key": "qj鲨鱼",
            "name": "鲨鱼┃APP",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg402i7942463j9j9jg8558j9jlgh1j913il86313j776l14ljjh3l5k383999648j9064g6h22738938il8jk30k2i6427h67jkhglji910996ig539"
        },
        {
            "key": "qj优质",
            "name": "优质┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg402i7942503g83j2g04i8090khhh8451ik902l6l53510hg9gi410i5607974jhk994283883904gk88l3k549h1g87i754kkjh3jhki2g8724k064g85411gigljkl3g620"
        },
        {
            "key": "qj客星",
            "name": "客星┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Xdai",
            "playerType": 2,
            "ext": "7lj763gg0939790i413gi486k509jgi3ggk6j244j6j36j76041707h0lk124l0l0h867j83h576h3j66h4lk89lh5lh1jl3l170647kj09lklji74j56lg673li1h16"
        },
        {
            "key": "qj蝴蝶",
            "name": "蝴蝶┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Hudie",
            "playerType": 2,
            "ext": "7lj763gg402i794255648kj0hj539g9hl7k6jk52ji"
        },
        {
            "key": "qj柠檬",
            "name": "柠檬┃App",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Nmvod",
            "playerType": 2,
            "ext": "7lj763gg402i7942503g839jg74h8h88highi40799jh6i6k454419l0l5184k0g549h0kg2i01i838i2h42g891lgg1"
        },
        {
            "key": "qjAI2",
            "name": "AI2┃仅搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_AI",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "playerType": 2,
            "ext": "7lj763gg0939795i067li482k704i2igg2k6980j95i631231j400hlgh707531k54864lh88k5h9781740hh287h9k035k8kg697852k5lj9kh276i575jk6kgk444kkkl8j1gggj370h768h473015hll64h265j82hg5667h79k93j4jk16l6215l327l71i922ilh6499j4k9h66297k5gjh4ii39j5107h837lh5104k477449hj1104h1i384l164k779h4595jlkh577l8i6570l23h4hg71hj92243h9076glj4k1k3il2404gl2i4i445ii0ji55578"
        },
        {
            "key": "qjsusou",
            "name": "速搜┃4K",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_AI2",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "playerType": 2,
            "ext": "7lj763gg0939790i4123i4i0g54l8387hihii54j96jh6k72015j14k3l8045l081i9k52g7994197823i15g18hk2k808hjhl2l754kkjlij8k9358g28hj7hg25j0llkkk8ig8k5782i649k4g2l45i3g51h3227k7gj367kh69llk99g626hh61131l6k2k9069g8l303814k812j733g1kik5lg086591h990jj2725lh73h14i59j104j092h481i3032hg668ki4g907638k7g7jkj2551h71jg24g44952j2hk34k5g6ik13001g29ij71j853gil4j62g75gj9843l357k2i4k04j0k2044000j6il06k964j83i3i10332123j0 "
        },
        {
            "key": "qjAI3",
            "name": "AI3┃仅搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_AI",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "playerType": 2,
            "ext": "7lj763gg0939795i067li482k704i2igg2k6980j95i631231j5203kgkg585g1k12j84jhl881j8396340hh487l3h30kk8l42l6851klhgjlk836ig69hj36k31i5ikhlki3gjli7k"
        },
        {
            "key": "qjDygang",
            "name": "电影港[边下边播]",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Dygang",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0
        },
        {
            "key": "qjLib",
            "name": "Lib┃直连",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Lib",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "7lj763gg402i7942463j9j9jhi558k84lhg7845kili62l35535512l0h61j48410h9g5igk9j40908h351hh484h8l60h"
        },
        {
            "key": "qjNoSearch_csp_DiDuan",
            "name": "低端┃无搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Ddrk",
            "searchable": 1,
            "playerType": 2,
            "quickSearch": 1,
            "filterable": 0
        },
        {
            "key": "qj快看",
            "name": "快看┃直连",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Kuaikan",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "qj360_spider",
            "name": "360┃解析",
            "api": "csp_SP360",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1,
            "playerType": 2,
            "ext": ""
        },
        {
            "key": "qjZXZJ",
            "name": "在线┃直连",
            "api": "csp_Zxzj",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1,
            "playerType": 2,
            "ext": "7lj763gg402i7942463j9j9jgg449698khhh845ki38473"
        },
        {
            "key": "qjcsp_CZSPP",
            "name": "厂长┃直连",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Czsapp",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "7lj763gg402i7942463j9j9jh346968hg5ll845ki38473"
        },
        {
            "key": "qjcsp_sbb",
            "name": "布布┃直连",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Czsapp",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "7lj763gg402i7942463j9j9jg3498k93lhggih54828g332i1j"
        },
        {
            "key": "qjcsp_Auete",
            "name": "Auete┃无搜索",
            "type": 3,
            "jar": "./qj.jar;md5;B5E81DE524446EC51B7503D99E1E5C4C",
            "api": "csp_Auete",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "push_agent",
            "name": "推送",
            "type": 3,
            "api": "csp_Push",
            "changeable": 0,
            "timeout": 30,
            "ext": "./lib/tokenm.json"
        }
    ],
    "lives": [
        {
            "name": "golive",
            "url": "./tv/result.txt",
            "type": 0,
            "epg": "https://epg.120572.xyz/index.php?ch={name}&date={date}",
            "logo": "https://epg.120572.xyz/index.php?ch={name}&type=icon",
            "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "catchup": {
                "type": "append",
                "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"
            }
        }
    ],
    "doh": [
        {
            "name": "Google",
            "url": "https://dns.google/dns-query",
            "ips": [
                "*******",
                "*******"
            ]
        },
        {
            "name": "Cloudflare",
            "url": "https://cloudflare-dns.com/dns-query",
            "ips": [
                "*******",
                "*******",
                "2606:4700:4700::1111",
                "2606:4700:4700::1001"
            ]
        },
        {
            "name": "AdGuard",
            "url": "https://dns.adguard.com/dns-query",
            "ips": [
                "*************",
                "*************"
            ]
        },
        {
            "name": "DNSWatch",
            "url": "https://resolver2.dns.watch/dns-query",
            "ips": [
                "************",
                "************"
            ]
        },
        {
            "name": "Quad9",
            "url": "https://dns.quad9.net/dns-quer",
            "ips": [
                "*******",
                "***************"
            ]
        }
    ],
    "proxy": [
        "raw.githubusercontent.com",
        "googlevideo.com",
        "cdn.v82u1l.com",
        "cdn.iz8qkg.com",
        "cdn.kin6c1.com",
        "c.biggggg.com",
        "c.olddddd.com",
        "haiwaikan.com",
        "www.histar.tv",
        "youtube.com",
        "uhibo.com",
        ".*boku.*",
        ".*nivod.*",
        ".*ulivetv.*"
    ],
    "hosts": [
        "cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.bestlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.wasulive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.fifalive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.hnbblive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"
    ],
    "rules": [
        {
            "name": "proxy",
            "hosts": [
                "raw.githubusercontent.com",
                "googlevideo.com",
                "cdn.v82u1l.com",
                "cdn.iz8qkg.com",
                "cdn.kin6c1.com",
                "c.biggggg.com",
                "c.olddddd.com",
                "haiwaikan.com",
                "www.histar.tv",
                "youtube.com",
                "uhibo.com",
                ".*boku.*",
                ".*nivod.*",
                "*.t4tv.hz.cz",
                ".*ulivetv.*"
            ]
        },
        {
            "host": "www.djuu.com",
            "rule": [
                "mp4.djuu.com",
                "m4a"
            ]
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "huoshan.com",
                "/item/video/"
            ],
            "filter": []
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "sovv.qianpailive.com",
                "vid="
            ],
            "filter": []
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "douyin.com",
                "/play/"
            ]
        },
        {
            "host": "m.ysxs8.vip",
            "rule": [
                "ysting.ysxs8.vip:81",
                "xmcdn.com"
            ],
            "filter": []
        },
        {
            "host": "hdmoli.com",
            "rule": [
                ".m3u8"
            ]
        },
        {
            "host": "https://api.live.bilibili.com",
            "rule": [
                "bilivideo.com",
                "/index.m3u8"
            ],
            "filter": [
                "data.bilibili.com/log/web",
                "i0.hdslb.com/bfs/live/"
            ]
        },
        {
            "host": "www.agemys.cc",
            "rule": [
                "cdn-tos",
                "obj/tos-cn"
            ]
        },
        {
            "host": "www.fun4k.com",
            "rule": [
                "https://hd.ijycnd.com/play",
                "index.m3u8"
            ]
        },
        {
            "host": "zjmiao.com",
            "rule": [
                "play.videomiao.vip/API.php",
                "time=",
                "key=",
                "path="
            ]
        },
        {
            "name": "火山嗅探",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "抖音嗅探",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "農民嗅探",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "夜市",
            "hosts": [
                "yeslivetv.com"
            ],
            "script": [
                "document.getElementsByClassName('vjs-big-play-button')[0].click()"
            ]
        },
        {
            "name": "毛驢",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        },
        {
            "name": "磁力广告",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "更多",
                "社 區",
                "x u u",
                "最 新",
                "直 播",
                "更 新",
                "社 区",
                "有 趣",
                "英皇体育",
                "全中文AV在线",
                "澳门皇冠赌场",
                "哥哥快来",
                "美女荷官",
                "裸聊",
                "新片首发",
                "UUE29"
            ]
        },
        {
            "host": "www.iesdouyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "www.ysgc.vip",
            "rule": [
                "getm3u8?url=http"
            ]
        },
        {
            "host": "v.douyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "dyxs20.com",
            "rule": [
                ".m3u8"
            ]
        },
        {
            "host": "www.agemys.cc",
            "rule": [
                "cdn-tos",
                "obj/tos-cn"
            ]
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "http.*?/play.{0,3}\\?[^url]{2,8}=.*",
                "qianpailive.com",
                "vid="
            ]
        },
        {
            "name": "暴风",
            "hosts": [
                "bfzy",
                "bfbfvip",
                "bfengbf"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts"
            ]
        },
        {
            "name": "量子",
            "hosts": [
                "vip.lz",
                "hd.lz",
                ".cdnlz"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:7\\.166667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?08646.*?\\.ts",
                "17.19",
                "19.63"
            ]
        },
        {
            "name": "非凡",
            "hosts": [
                "vip.ffzy",
                "hd.ffzy",
                "super.ffzy",
                "cachem3u8.2s0"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.400000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?1171(057).*?\\.ts",
                "#EXTINF.*?\\s+.*?6d7b(077).*?\\.ts",
                "#EXTINF.*?\\s+.*?6718a(403).*?\\.ts",
                "17.99",
                "14.45"
            ]
        },
        {
            "name": "索尼",
            "hosts": [
                "suonizy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:1\\.000000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?p1ayer.*?\\.ts",
                "#EXTINF.*?\\s+.*?\\/video\\/original.*?\\.ts"
            ]
        },
        {
            "name": "快看",
            "hosts": [
                "kuaikan"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:2\\.4,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:1\\.467,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "leshiyun",
            "hosts": [
                "leshiyuncdn"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "1080zyk",
            "hosts": [
                "high24-playback",
                "high20-playback",
                "yzzy.play",
                "yzzy-dy"
            ],
            "regex": [
                "16.63",
                "17.66"
            ]
        },
        {
            "name": "ikun",
            "hosts": [
                "bfikuncdn"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?XR8pDxQk.*?\\.ts"
            ]
        },
        {
            "name": "黑木耳hmr",
            "hosts": [
                "hmrvideo"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3\\.366667,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "ryplay",
            "hosts": [
                "cdn.ryplay"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.633333,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8\\.333333,[\\s\\S].*?\\.ts",
                "#EXTINF:2\\.933333,[\\s\\S].*?\\.ts"
            ]
        },
        {
            "name": "555DM",
            "hosts": [
                "cqxfjz"
            ],
            "regex": [
                "10.56"
            ]
        },
        {
            "name": "海外看",
            "hosts": [
                "haiwaikan"
            ],
            "regex": [
                "10.0099",
                "10.3333",
                "16.0599",
                "8.1748",
                "10.85"
            ]
        },
        {
            "name": "磁力广告",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "更多",
                "社 區",
                "x u u",
                "最 新",
                "更 新",
                "社 区",
                "有趣",
                "有 趣",
                "英皇体育",
                "全中文AV在线",
                "澳门皇冠赌场",
                "哥哥快来",
                "美女荷官",
                "裸聊",
                "新片首发",
                "UUE29"
            ]
        },
        {
            "name": "♻️量非",
            "hosts": [
                "lz",
                "vip.lz",
                "v.cdnlz",
                "hd.lz",
                "ffzy",
                "vip.ffzy",
                "hd.ffzy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.600000,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️索尼",
            "hosts": [
                "suonizy",
                "qrssv.com"
            ],
            "regex": [
                "15.1666",
                "15.2666"
            ]
        },
        {
            "name": "♻️乐视",
            "hosts": [
                "leshiyun"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "♻️优质",
            "hosts": [
                "yzzy",
                "playback"
            ],
            "regex": [
                "16.63",
                "18.66",
                "17.66",
                "19.13"
            ]
        },
        {
            "name": "♻️快看",
            "hosts": [
                "kuaikan",
                "vip.kuaikan"
            ],
            "regex": [
                "15.32",
                "15.231",
                "18.066"
            ]
        },
        {
            "name": "♻️360",
            "hosts": [
                "lyhuicheng"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?hrz8QcR9.*?\\.ts\\s+",
                "#EXT-X-KEY:METHOD=NONE[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️开源棋牌",
            "hosts": [
                "askzycdn",
                "jkunbf",
                "bfikuncdn",
                "bfaskcdn"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\r*\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=AES-128,URI=\"[^\"]+\"\r*\n*#EXTINF:3.333,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️暴风",
            "hosts": [
                "bfengbf.com",
                "bfzy",
                "c1"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️农民",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "♻️火山",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "♻️抖音",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "♻️磁力",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "最 新",
                "直 播",
                "更 新"
            ]
        },
        {
            "name": "♻️饭团点击",
            "hosts": [
                "dadagui",
                "freeok",
                "dadagui"
            ],
            "script": [
                "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"
            ]
        },
        {
            "name": "♻️毛驴点击",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        }
    ],
    "parses": [
        {
            "name": "解析聚合",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "Web聚合",
            "type": 3,
            "url": "Web"
        },
        {
            "name": "Json轮询",
            "type": 2,
            "url": "Sequence"
        },
        {
            "name": "巧技",
            "type": 1,
            "url": "https://zy.qiaoji8.com/neibu.php?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "2",
            "type": 1,
            "url": "https://json-vipjx.952707.xyz/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "1",
            "type": 1,
            "url": "http://jx.dedyn.io/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "3",
            "type": 1,
            "url": "http://jx.dedyn.io/jx.php?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "巧技二",
            "type": 1,
            "url": "https://zy.qiaoji8.com/gouzi.php?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili",
                    "1905",
                    "NetFilx"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "牛牛2",
            "type": 1,
            "url": "https://zy.qiaoji8.com/xiafan.php?url=",
            "ext": {
                "flag": [
                    "QD4K",
                    "iyf",
                    "duanju",
                    "gzcj",
                    "GTV",
                    "GZYS",
                    "weggz",
                    "Ace"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "巧技三",
            "type": 1,
            "url": "https://zy.qiaoji8.com/xiafan.php?url=",
            "ext": {
                "flag": [
                    "QD4K",
                    "iyf",
                    "duanju",
                    "gzcj",
                    "GTV",
                    "GZYS",
                    "weggz",
                    "Ace"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "-咸鱼-",
            "type": 0,
            "url": "https://jx.xymp4.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.3124.68"
                }
            }
        },
        {
            "name": "-虾米-",
            "type": 0,
            "url": "https://jx.xmflv.com/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.57"
                }
            }
        },
        {
            "name": "-淘片-",
            "type": 0,
            "url": "https://jx.yparse.com/index.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-冰豆-",
            "type": 0,
            "url": "https://bd.jx.cn/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-77解析-",
            "type": 0,
            "url": "https://jx.77flv.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-zui-",
            "type": 0,
            "url": "https://jx.zui.cm/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-盘古-",
            "type": 0,
            "url": "https://www.playm3u8.cn/jiexi.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-夜幕-",
            "type": 0,
            "url": "https://yemu.xyz/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
                }
            }
        }
    ],
    "flags": [
        "youku",
        "qq",
        "iqiyi",
        "qiyi",
        "letv",
        "sohu",
        "tudou",
        "pptv",
        "mgtv",
        "wasu"
    ],
    "wallpaper": "http://饭太硬.top/深色壁纸/api.php",
    "disabled_wallpaper": "http://www.kf666888.cn/api/tvbox/img"
}