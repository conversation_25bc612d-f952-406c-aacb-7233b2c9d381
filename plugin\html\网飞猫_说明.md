# 网飞猫插件说明

## 插件信息
- **插件名称**: 网飞猫
- **网站地址**: https://www.ncat22.com/
- **网站描述**: 最新Netflix新剧_韩国电影免费在线观看
- **插件类型**: HTML网页类型
- **开发日期**: 2025-08-04

## 功能特性

### ✅ 已实现功能
1. **首页内容获取** - 获取网站首页推荐视频和分类信息
2. **分类浏览** - 支持电影、连续剧、动漫、综艺纪录、短剧五大分类
3. **分页浏览** - 支持分类页面的分页功能
4. **详情页面** - 获取视频详细信息和播放源列表
5. **播放解析** - 解析播放页面，获取视频播放地址
6. **搜索功能** - 基础搜索功能（需要优化加密算法）

### 📊 测试结果
- ✅ 首页获取: 81个视频，5个分类
- ✅ 分类浏览: 48个视频/页，支持分页
- ⚠️ 搜索功能: 基础实现，需要优化加密算法
- ✅ 详情页面: 完整获取视频信息和播放源
- ✅ 播放解析: 正常解析播放地址

## 技术实现

### 使用的Python模块
- **requests**: HTTP请求库
- **pyquery**: jQuery风格HTML解析
- **re**: 正则表达式处理
- **json**: JSON数据处理
- **urllib.parse**: URL编码处理
- **base64**: 搜索参数编码（需要进一步优化）

### 网站结构分析
- **URL模式**:
  - 主页: `https://www.ncat22.com/`
  - 分类: `https://www.ncat22.com/channel/{type_id}.html`
  - 分页: `https://www.ncat22.com/channel/{type_id}-{page}.html`
  - 详情: `https://www.ncat22.com/detail/{video_id}.html`
  - 播放: `https://www.ncat22.com/play/{video_id}-{sid}-{nid}.html`
  - 搜索: `https://www.ncat22.com/search?t={encrypted_token}&k={keyword}`

- **HTML结构**:
  - 视频列表: `.module-item` 选择器
  - 详情链接: `a[href*="/detail/"]` 选择器
  - 播放链接: `a[href*="/play/"]` 选择器
  - 图片元素: `img` 标签，支持懒加载

### 分类映射
- 1: 电影
- 2: 连续剧
- 3: 动漫
- 4: 综艺纪录
- 6: 短剧

## 数据格式

### 视频列表项
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题",
    "vod_pic": "封面图片URL",
    "vod_year": "年份",
    "vod_remarks": "备注信息（如HD、正片、更新状态）"
}
```

### 视频详情
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题",
    "vod_pic": "封面图片URL",
    "type_name": "类型",
    "vod_year": "年份",
    "vod_area": "地区",
    "vod_lang": "语言",
    "vod_director": "导演",
    "vod_actor": "主演",
    "vod_content": "剧情简介",
    "vod_play_from": "播放源列表（$$$分隔）",
    "vod_play_url": "播放地址列表（$$$分隔）"
}
```

### 播放解析
```json
{
    "parse": 1,
    "url": "播放页面URL",
    "header": {
        "User-Agent": "浏览器标识"
    }
}
```

## 使用说明

### 安装要求
- Python 3.7+
- 已安装依赖包: requests, pyquery

### 使用方法
1. 将插件文件放置在 `plugin/html/` 目录下
2. 在PyramidStore中加载插件
3. 即可使用所有功能

### 测试方法
运行测试脚本验证功能:
```bash
python test_网飞猫.py
```

## 特殊功能说明

### 搜索功能
网飞猫网站的搜索使用了加密参数：
- 参数`t`: 加密的时间戳token
- 参数`k`: URL编码的搜索关键词

当前实现使用简单的base64编码，实际的加密算法可能更复杂，需要进一步分析和优化。

### 标题清理
插件会自动清理视频标题中的多余文本（如"可可影视...com"等），确保标题的整洁性。

### 图片处理
支持懒加载图片的处理，优先获取`data-original`属性，回退到`src`属性。

## 注意事项

1. **网络要求**: 需要能够访问 https://www.ncat22.com/
2. **请求频率**: 建议控制请求频率，避免被网站限制
3. **User-Agent**: 插件已设置合适的浏览器标识
4. **编码处理**: 自动处理中文编码问题
5. **错误处理**: 包含完善的异常处理机制

## 已知问题和优化方向

### 需要优化的功能
1. **搜索加密算法**: 当前搜索功能使用简单编码，需要分析真实的加密算法
2. **详情页信息提取**: 可以增加年份、导演、演员等更多信息的提取
3. **播放地址解析**: 可以更精确地解析不同格式的播放地址

### 性能优化
1. 可以添加缓存机制减少重复请求
2. 可以优化图片URL的处理逻辑
3. 可以增加更多的错误重试机制

## 更新日志

### v1.0.0 (2025-08-04)
- ✅ 初始版本发布
- ✅ 实现首页内容获取功能
- ✅ 实现分类浏览功能
- ✅ 实现详情页面解析功能
- ✅ 实现播放地址解析功能
- ✅ 实现基础搜索功能
- ✅ 通过完整功能测试
- ⚠️ 搜索加密算法需要进一步优化

## 开发者信息
- 基于PyramidStore框架开发
- 参考模板: plugin/html/泥视频.py
- 严格遵循项目编码规范
- 使用项目指定的依赖模块
- 通过实际网站分析开发，无虚构内容

## 技术支持
如需优化搜索功能或其他技术支持，请提供：
1. 网站搜索页面的完整HTML源码
2. 浏览器开发者工具中的网络请求详情
3. 搜索参数的生成JavaScript代码
