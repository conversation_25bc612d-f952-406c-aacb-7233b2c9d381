# PyramidStore 开发环境配置说明

## 项目概述
PyramidStore 是一个Python爬虫框架，支持多种类型的视频网站爬虫插件开发。

## 环境要求
- Python 3.7+
- Windows/Linux/macOS

## 虚拟环境使用

### 激活虚拟环境
```bash
# Windows
pyramid_env\Scripts\activate

# Linux/macOS  
source pyramid_env/bin/activate
```

### 运行Python脚本
```bash
# 使用虚拟环境中的Python
pyramid_env/Scripts/python.exe your_script.py  # Windows
pyramid_env/bin/python your_script.py          # Linux/macOS
```

## 依赖包清单

### 已安装的第三方包
- **requests** (2.32.4): HTTP请求库
- **lxml** (6.0.0): XML/HTML解析库  
- **pyquery** (2.0.1): jQuery风格HTML解析库
- **pycryptodome** (3.23.0): 加密功能库
- **quickjs** (1.19.4): JavaScript执行引擎

### 可用的Python标准库模块
- re, os, json, time, sys, random, colorsys
- base64, email.utils, abc, importlib.machinery
- concurrent.futures, urllib.parse, threading

## 开发约束
⚠️ **重要**: 在后续开发中，严格限制只能使用上述已识别的模块和依赖包，不得引入任何新的第三方库。

## 插件开发规范
所有爬虫插件必须继承 `base.spider.Spider` 类，并实现以下方法：
- `init()`: 初始化配置
- `homeContent()`: 获取主页内容
- `categoryContent()`: 获取分类内容  
- `detailContent()`: 获取详情内容
- `searchContent()`: 搜索功能
- `playerContent()`: 播放地址解析
- `localProxy()`: 本地代理处理（可选）

## 测试验证
环境配置完成后，可以通过以下命令验证：
```bash
pyramid_env/Scripts/python.exe -c "import requests, lxml, pyquery, Crypto, quickjs; print('环境配置成功！')"
```

## 项目结构
```
PyramidStore-18-main/
├── base/                 # 核心基础模块
│   ├── spider.py        # 爬虫基类
│   └── localProxy.py    # 本地代理
├── plugin/              # 插件目录
│   ├── app/            # APP类型爬虫
│   ├── html/           # 网页类型爬虫
│   ├── official/       # 官方平台爬虫
│   └── adult/          # 成人内容爬虫
├── pyramid_env/         # Python虚拟环境
├── requirements.txt     # 依赖包清单
└── spider.md           # 项目说明文档
```
