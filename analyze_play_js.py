# -*- coding: utf-8 -*-
# 分析play.js文件 - 查找播放逻辑
import requests
import re
import json
import base64
import urllib.parse

def analyze_play_js():
    """分析play.js文件中的播放逻辑"""
    
    print("=" * 80)
    print("分析play.js文件 - 查找播放逻辑")
    print("=" * 80)
    
    # play.js文件URL
    play_js_url = 'https://www.novipnoad.net/theme/js/play.js?v=20250509'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': 'https://www.novipnoad.net/',
    }
    
    try:
        # 1. 下载play.js文件
        print(f"\n1. 下载play.js文件: {play_js_url}")
        response = requests.get(play_js_url, headers=headers, timeout=15)
        print(f"状态码: {response.status_code}")
        print(f"文件大小: {len(response.text)} 字符")
        
        if response.status_code != 200:
            print("下载失败")
            return
        
        js_content = response.text
        
        # 保存原始文件
        with open('play_original.js', 'w', encoding='utf-8') as f:
            f.write(js_content)
        print("原始play.js已保存到: play_original.js")
        
        # 2. 分析JavaScript代码结构
        print(f"\n2. 分析JavaScript代码结构...")
        
        # 查找函数定义
        function_patterns = [
            r'function\s+(\w+)\s*\([^)]*\)\s*\{',
            r'(\w+)\s*:\s*function\s*\([^)]*\)\s*\{',
            r'var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{',
            r'let\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{',
            r'const\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{',
        ]
        
        functions = set()
        for pattern in function_patterns:
            matches = re.findall(pattern, js_content)
            for match in matches:
                functions.add(match)
        
        print(f"找到的函数: {list(functions)}")
        
        # 3. 查找播放相关的关键词
        print(f"\n3. 查找播放相关的关键词...")
        
        play_keywords = [
            'play', 'video', 'player', 'src', 'url', 'stream', 'embed',
            'ajax', 'api', 'load', 'init', 'setup', 'config'
        ]
        
        for keyword in play_keywords:
            if keyword in js_content.lower():
                print(f"  发现关键词: {keyword}")
                
                # 查找包含该关键词的行
                lines = js_content.split('\n')
                for i, line in enumerate(lines):
                    if keyword in line.lower() and len(line.strip()) > 10:
                        print(f"    行 {i+1}: {line.strip()[:100]}...")
                        break
        
        # 4. 查找AJAX请求
        print(f"\n4. 查找AJAX请求...")
        
        ajax_patterns = [
            r'ajax\s*\(\s*["\']([^"\']+)["\']',
            r'\.get\s*\(\s*["\']([^"\']+)["\']',
            r'\.post\s*\(\s*["\']([^"\']+)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*?open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']',
        ]
        
        ajax_urls = set()
        for pattern in ajax_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                ajax_urls.add(match)
        
        if ajax_urls:
            print("找到的AJAX URL:")
            for url in ajax_urls:
                print(f"  🔗 {url}")
        
        # 5. 查找URL构建逻辑
        print(f"\n5. 查找URL构建逻辑...")
        
        url_patterns = [
            r'["\']([^"\']*(?:m3u8|mp4|flv|avi|mkv)[^"\']*)["\']',
            r'["\']url["\']:\s*["\']([^"\']+)["\']',
            r'["\']src["\']:\s*["\']([^"\']+)["\']',
            r'["\']file["\']:\s*["\']([^"\']+)["\']',
            r'url\s*=\s*["\']([^"\']+)["\']',
            r'src\s*=\s*["\']([^"\']+)["\']',
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if 'http' in match or '.m3u8' in match or '.mp4' in match:
                    found_urls.add(match)
        
        if found_urls:
            print("找到的URL模式:")
            for url in found_urls:
                print(f"  🎯 {url}")
        
        # 6. 查找加密/解密逻辑
        print(f"\n6. 查找加密/解密逻辑...")
        
        crypto_patterns = [
            r'atob\s*\([^)]+\)',
            r'btoa\s*\([^)]+\)',
            r'decode\w*\s*\([^)]+\)',
            r'encode\w*\s*\([^)]+\)',
            r'decrypt\w*\s*\([^)]+\)',
            r'encrypt\w*\s*\([^)]+\)',
        ]
        
        for pattern in crypto_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                print(f"  发现加密/解密函数: {matches[:3]}")  # 只显示前3个
        
        # 7. 查找特定的播放器初始化
        print(f"\n7. 查找播放器初始化...")
        
        # 查找可能的播放器初始化代码
        player_init_patterns = [
            r'new\s+\w*[Pp]layer\s*\([^)]*\)',
            r'\w*[Pp]layer\s*\(\s*[^)]*\)',
            r'video\s*=\s*[^;]+',
            r'player\s*=\s*[^;]+',
        ]
        
        for pattern in player_init_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 10:
                    print(f"  播放器初始化: {match[:100]}...")
        
        # 8. 查找事件处理函数
        print(f"\n8. 查找事件处理函数...")
        
        # 查找点击事件处理
        click_patterns = [
            r'click\s*:\s*function[^}]*\}',
            r'onclick\s*=\s*["\'][^"\']*["\']',
            r'addEventListener\s*\(\s*["\']click["\'][^)]*\)',
        ]
        
        for pattern in click_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                print(f"  点击事件: {match[:100]}...")
        
        # 9. 尝试美化JavaScript代码
        print(f"\n9. 尝试美化JavaScript代码...")
        
        # 简单的代码美化 - 添加换行
        beautified = js_content
        beautified = re.sub(r';\s*', ';\n', beautified)
        beautified = re.sub(r'\{\s*', '{\n', beautified)
        beautified = re.sub(r'\}\s*', '\n}\n', beautified)
        
        # 保存美化后的代码
        with open('play_beautified.js', 'w', encoding='utf-8') as f:
            f.write(beautified)
        print("美化后的play.js已保存到: play_beautified.js")
        
        # 10. 查找关键的播放逻辑
        print(f"\n10. 查找关键的播放逻辑...")
        
        # 查找可能包含播放地址获取逻辑的代码段
        key_sections = []
        
        # 按函数分割代码
        function_blocks = re.split(r'function\s+\w+\s*\([^)]*\)\s*\{', js_content)
        
        for i, block in enumerate(function_blocks):
            if any(keyword in block.lower() for keyword in ['ajax', 'url', 'video', 'play', 'src']):
                key_sections.append(f"代码段 {i+1}: {block[:200]}...")
        
        if key_sections:
            print("关键代码段:")
            for section in key_sections[:5]:  # 只显示前5个
                print(f"  {section}")
        
        # 11. 生成分析报告
        print(f"\n11. 生成分析报告...")
        
        analysis_report = {
            'file_size': len(js_content),
            'functions_found': list(functions),
            'ajax_urls_found': list(ajax_urls),
            'video_urls_found': list(found_urls),
            'has_crypto': any(re.search(pattern, js_content, re.IGNORECASE) for pattern in crypto_patterns),
            'key_sections_count': len(key_sections)
        }
        
        with open('play_js_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        
        print("play.js分析报告已保存到: play_js_analysis.json")
        print(f"文件大小: {analysis_report['file_size']} 字符")
        print(f"函数数量: {len(analysis_report['functions_found'])}")
        print(f"AJAX URL数量: {len(analysis_report['ajax_urls_found'])}")
        print(f"视频URL数量: {len(analysis_report['video_urls_found'])}")
        print(f"包含加密逻辑: {analysis_report['has_crypto']}")
        
        print("\n" + "=" * 80)
        print("play.js分析完成")
        print("=" * 80)
        
        return analysis_report
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_play_js()
