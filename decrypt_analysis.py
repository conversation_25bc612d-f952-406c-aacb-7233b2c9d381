# -*- coding: utf-8 -*-
# 深入分析解密算法和弹幕播放器
import requests
import re
import json
import base64
from urllib.parse import urljoin, unquote

def base64decode_js(s):
    """JavaScript风格的base64解码"""
    try:
        # 处理可能的padding问题
        missing_padding = len(s) % 4
        if missing_padding:
            s += '=' * (4 - missing_padding)
        return base64.b64decode(s).decode('utf-8')
    except Exception as e:
        print(f"Base64解码失败: {e}")
        return None

def decrypt_url(url, encrypt_type):
    """根据encrypt类型解密URL"""
    try:
        if encrypt_type == '1':
            # 只需要URL解码
            return unquote(url)
        elif encrypt_type == '2':
            # 先base64解码，再URL解码
            decoded = base64decode_js(url)
            if decoded:
                return unquote(decoded)
        else:
            # encrypt为0或其他值，直接返回
            return url
    except Exception as e:
        print(f"解密失败: {e}")
        return None

def analyze_danmu_player():
    """深入分析弹幕播放器"""
    base_url = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    print("=" * 80)
    print("深入分析解密算法和弹幕播放器")
    print("=" * 80)

    # 1. 获取播放页面的player_aaaa数据
    print("\n1. 获取播放页面数据:")
    try:
        play_url = f"{base_url}/bangumi/38328/play/1/1/"
        response = requests.get(play_url, headers=headers, timeout=10)

        if response.status_code == 200:
            play_content = response.text

            # 提取player_aaaa变量
            player_patterns = [
                r'var\s+player_aaaa\s*=\s*({[^;]+});',
                r'player_aaaa\s*=\s*({[^;]+});',
                r'var\s+player_aaaa\s*=\s*({.*?});'
            ]

            player_match = None
            for pattern in player_patterns:
                player_match = re.search(pattern, play_content, re.DOTALL)
                if player_match:
                    break

            if player_match:
                player_json_str = player_match.group(1)
                print(f"  找到player_aaaa: {player_json_str}")

                # 提取关键信息
                encrypt_match = re.search(r'"encrypt"[:\s]*(\d+)', player_json_str)
                url_match = re.search(r'"url"[:\s]*"([^"]+)"', player_json_str)
                link_match = re.search(r'"link"[:\s]*"([^"]+)"', player_json_str)

                encrypt_type = encrypt_match.group(1) if encrypt_match else '0'
                encrypted_url = url_match.group(1) if url_match else None
                play_link = link_match.group(1) if link_match else None

                print(f"  encrypt类型: {encrypt_type}")
                print(f"  加密URL: {encrypted_url}")
                print(f"  播放链接: {play_link}")

                # 尝试解密URL
                if encrypted_url:
                    print(f"\n2. 尝试解密URL:")
                    decrypted_url = decrypt_url(encrypted_url, encrypt_type)
                    print(f"  解密结果: {decrypted_url}")

                    # 如果解密成功，检查是否是有效的播放地址
                    if decrypted_url and ('http' in decrypted_url or '/' in decrypted_url):
                        print(f"  解密成功，尝试访问: {decrypted_url}")

                        # 如果是相对路径，构建完整URL
                        if not decrypted_url.startswith('http'):
                            full_url = urljoin(base_url, decrypted_url)
                        else:
                            full_url = decrypted_url

                        try:
                            test_response = requests.head(full_url, headers=headers, timeout=5)
                            print(f"  访问结果: {test_response.status_code}")
                            print(f"  Content-Type: {test_response.headers.get('Content-Type', 'unknown')}")
                        except Exception as e:
                            print(f"  访问失败: {e}")

                # 分析弹幕播放器
                if play_link:
                    print(f"\n3. 分析弹幕播放器:")
                    # 构建弹幕播放器URL
                    if encrypted_url:
                        danmu_url = f"{base_url}/static/danmu/qy.php?url={encrypted_url}"
                    else:
                        danmu_url = f"{base_url}/static/danmu/qy.php?url={play_link}"

                    print(f"  弹幕播放器URL: {danmu_url}")

                    try:
                        danmu_response = requests.get(danmu_url, headers=headers, timeout=10)
                        if danmu_response.status_code == 200:
                            danmu_content = danmu_response.text
                            print(f"  弹幕播放器响应长度: {len(danmu_content)}")

                            # 在弹幕播放器中查找真实播放地址
                            print(f"\n4. 在弹幕播放器中查找真实播放地址:")

                            # 查找各种可能的播放地址模式
                            video_patterns = [
                                r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
                                r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
                                r'(https?://[^"\'>\s]+\.flv[^"\'>\s]*)',
                                r'"url"[:\s]*"([^"]+\.(?:m3u8|mp4|flv)[^"]*)"',
                                r'"src"[:\s]*"([^"]+\.(?:m3u8|mp4|flv)[^"]*)"',
                                r'video["\s]*[:=]["\s]*"([^"]+\.(?:m3u8|mp4|flv)[^"]*)"',
                                r'source["\s]*[:=]["\s]*"([^"]+\.(?:m3u8|mp4|flv)[^"]*)"'
                            ]

                            found_videos = []
                            for pattern in video_patterns:
                                matches = re.findall(pattern, danmu_content)
                                if matches:
                                    found_videos.extend(matches)
                                    print(f"    模式 '{pattern}' 找到: {matches}")

                            # 去重并验证找到的视频地址
                            unique_videos = list(set(found_videos))
                            if unique_videos:
                                print(f"\n5. 验证找到的视频地址:")
                                for i, video_url in enumerate(unique_videos):
                                    print(f"  视频地址 {i+1}: {video_url}")
                                    try:
                                        video_response = requests.head(video_url, headers=headers, timeout=5)
                                        print(f"    状态码: {video_response.status_code}")
                                        print(f"    Content-Type: {video_response.headers.get('Content-Type', 'unknown')}")
                                        print(f"    Content-Length: {video_response.headers.get('Content-Length', 'unknown')}")

                                        # 如果是有效的视频文件
                                        if video_response.status_code == 200:
                                            content_type = video_response.headers.get('Content-Type', '')
                                            if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                                                print(f"    ✅ 这是一个有效的视频地址！")
                                                return video_url
                                    except Exception as e:
                                        print(f"    验证失败: {e}")
                            else:
                                print(f"    未找到直接的视频地址")

                                # 查找可能的iframe或其他播放器
                                iframe_patterns = [
                                    r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>',
                                    r'player["\s]*[:=]["\s]*"([^"]+)"',
                                    r'embed["\s]*[:=]["\s]*"([^"]+)"'
                                ]

                                for pattern in iframe_patterns:
                                    matches = re.findall(pattern, danmu_content)
                                    if matches:
                                        print(f"    找到嵌入播放器: {matches}")

                    except Exception as e:
                        print(f"  弹幕播放器访问失败: {e}")
            else:
                print(f"  未找到player_aaaa变量")
                # 尝试查找其他可能的播放相关变量
                other_patterns = [
                    r'var\s+(\w*player\w*)\s*=\s*{[^}]+}',
                    r'(\w*url\w*)\s*=\s*["\']([^"\']+)["\']',
                    r'(danmu[^"\']*\.php[^"\']*)'
                ]

                for pattern in other_patterns:
                    matches = re.findall(pattern, play_content)
                    if matches:
                        print(f"    找到其他变量: {matches}")

    except Exception as e:
        print(f"  分析失败: {e}")
        import traceback
        traceback.print_exc()

    return None

if __name__ == "__main__":
    result = analyze_danmu_player()
    if result:
        print(f"\n🎉 成功找到可直接播放的视频地址: {result}")
    else:
        print(f"\n❌ 未能找到可直接播放的视频地址")
