# -*- coding: utf-8 -*-
# 深入分析弹幕播放器返回的内容
import requests
import re
import json
import base64
from urllib.parse import urljoin, unquote

def analyze_danmu_content():
    """深入分析弹幕播放器返回的内容"""
    base_url = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    print("=" * 80)
    print("深入分析弹幕播放器返回的内容")
    print("=" * 80)

    # 使用之前获取的加密URL
    encrypted_url = "89d6a0gxQj8sJ3dgUXbfNUgnzxMJS96CuQ1p0v0dlvVDHeMud72_pHd6zRosULkUbztu5YqQ3zSmkunByYdi1P6ulCeNfP_SqXSsxX-6pJICFNOSdAQzWoBWd7r0XMSYA96-uewZHMBv"

    danmu_url = f"{base_url}/static/danmu/qy.php?url={encrypted_url}"

    print(f"\n1. 获取弹幕播放器内容:")
    print(f"  URL: {danmu_url}")

    try:
        response = requests.get(danmu_url, headers=headers, timeout=10)

        if response.status_code == 200:
            content = response.text
            print(f"  响应长度: {len(content)} 字符")

            # 保存内容到文件以便分析
            with open('danmu_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  内容已保存到 danmu_content.html")

            # 2. 查找JavaScript变量和函数
            print(f"\n2. 查找JavaScript变量和函数:")

            # 查找可能包含播放地址的变量
            js_patterns = [
                r'var\s+(\w*url\w*)\s*=\s*["\']([^"\']+)["\']',
                r'var\s+(\w*src\w*)\s*=\s*["\']([^"\']+)["\']',
                r'var\s+(\w*video\w*)\s*=\s*["\']([^"\']+)["\']',
                r'(\w*url\w*)\s*[:=]\s*["\']([^"\']+)["\']',
                r'(\w*src\w*)\s*[:=]\s*["\']([^"\']+)["\']'
            ]

            for pattern in js_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"    模式 '{pattern}' 找到:")
                    for var_name, var_value in matches:
                        if len(var_value) > 10:  # 过滤掉太短的值
                            print(f"      {var_name}: {var_value}")

            # 3. 查找iframe和embed标签
            print(f"\n3. 查找iframe和embed标签:")
            iframe_patterns = [
                r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>',
                r'<embed[^>]+src=["\']([^"\']+)["\'][^>]*>',
                r'<object[^>]+data=["\']([^"\']+)["\'][^>]*>'
            ]

            for pattern in iframe_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"    模式 '{pattern}' 找到: {matches}")

            # 4. 查找可能的API调用
            print(f"\n4. 查找可能的API调用:")
            api_patterns = [
                r'ajax\([^)]+\)',
                r'fetch\([^)]+\)',
                r'XMLHttpRequest[^;]+',
                r'\.get\([^)]+\)',
                r'\.post\([^)]+\)'
            ]

            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"    模式 '{pattern}' 找到: {matches[:3]}")  # 只显示前3个

            # 5. 查找base64编码的内容
            print(f"\n5. 查找base64编码的内容:")
            base64_patterns = [
                r'[A-Za-z0-9+/]{50,}={0,2}',  # 可能的base64字符串
                r'data:video/[^"\']+',
                r'data:application/[^"\']+',
                r'blob:[^"\']+',
                r'https?://[^"\'>\s]+\.(?:m3u8|mp4|flv|ts)[^"\'>\s]*'
            ]

            for pattern in base64_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"    模式 '{pattern}' 找到: {len(matches)} 个匹配")
                    for i, match in enumerate(matches[:3]):  # 只显示前3个
                        print(f"      {i+1}: {match[:100]}...")

            # 6. 查找特定的播放器相关字符串
            print(f"\n6. 查找播放器相关字符串:")
            player_keywords = [
                'video', 'source', 'stream', 'play', 'media',
                'm3u8', 'mp4', 'flv', 'hls', 'dash'
            ]

            for keyword in player_keywords:
                # 查找包含关键词的行
                lines = content.split('\n')
                matching_lines = [line.strip() for line in lines if keyword.lower() in line.lower() and len(line.strip()) > 10]
                if matching_lines:
                    print(f"    包含 '{keyword}' 的行:")
                    for i, line in enumerate(matching_lines[:3]):  # 只显示前3行
                        print(f"      {i+1}: {line[:150]}...")

            # 7. 查找可能的解密函数调用
            print(f"\n7. 查找解密函数调用:")
            decrypt_patterns = [
                r'base64decode\([^)]+\)',
                r'unescape\([^)]+\)',
                r'decodeURIComponent\([^)]+\)',
                r'atob\([^)]+\)',
                r'JSON\.parse\([^)]+\)'
            ]

            for pattern in decrypt_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"    模式 '{pattern}' 找到: {matches}")

            # 8. 尝试查找隐藏的播放地址
            print(f"\n8. 尝试查找隐藏的播放地址:")

            # 查找可能被混淆的URL
            obfuscated_patterns = [
                r'["\']([^"\']*(?:http|\/\/)[^"\']*\.(?:m3u8|mp4|flv)[^"\']*)["\']',
                r'url\s*[:=]\s*["\']([^"\']+)["\']',
                r'src\s*[:=]\s*["\']([^"\']+)["\']'
            ]

            for pattern in obfuscated_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"    模式 '{pattern}' 找到:")
                    for match in matches:
                        if any(ext in match.lower() for ext in ['.m3u8', '.mp4', '.flv', 'http']):
                            print(f"      可能的播放地址: {match}")

        else:
            print(f"  请求失败: {response.status_code}")

    except Exception as e:
        print(f"  分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_danmu_content()
