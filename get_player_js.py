# -*- coding: utf-8 -*-
# 获取并保存player.js文件
import requests

def get_and_save_player_js():
    """获取并保存player.js文件"""
    base_url = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Referer': 'http://www.bimiacg11.net/',
    }

    try:
        # 获取player.js文件
        player_js_url = f"{base_url}/static/js/player.js"
        print(f"获取player.js: {player_js_url}")
        response = requests.get(player_js_url, headers=headers, timeout=10)

        if response.status_code == 200:
            content = response.text
            print(f"player.js大小: {len(content)} 字符")

            # 保存到文件
            with open('player.js', 'w', encoding='utf-8') as f:
                f.write(content)
            print("player.js已保存到本地")

            # 查找base64decode相关内容
            print("\n查找base64decode相关内容:")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'base64decode' in line.lower():
                    print(f"  第{i+1}行: {line}")

            # 查找function关键字附近的内容
            print("\n查找function定义:")
            import re
            function_matches = re.findall(r'function\s+\w+[^{]*{[^}]*}', content)
            for i, match in enumerate(function_matches):
                if 'base64' in match.lower():
                    print(f"  函数{i+1}: {match}")

        else:
            print(f"获取player.js失败: {response.status_code}")

    except Exception as e:
        print(f"获取player.js异常: {e}")

if __name__ == "__main__":
    get_and_save_player_js()
