# NO视频插件说明

## 插件信息
- **插件名称**: NO视频
- **网站地址**: https://www.novipnoad.net/
- **网站描述**: 不负追剧好时光 - 海外热门剧集在线观看
- **插件类型**: HTML网页类型
- **开发日期**: 2025-08-04

## 功能特性

### ✅ 已实现功能
1. **首页内容获取** - 获取网站首页推荐视频和分类信息
2. **分类浏览** - 支持电影、港剧、台剧、欧美剧、日剧、韩剧、泰剧、土耳其剧、动画、综艺、音乐、短片、其他等13个分类
3. **分页浏览** - 支持分类页面的分页功能
4. **搜索功能** - 支持关键词搜索视频内容
5. **详情页面** - 获取视频详细信息、类型识别、年份提取
6. **播放解析** - 解析播放页面，获取视频播放地址

### 📊 测试结果
- ✅ 首页获取: 105个视频，13个分类
- ✅ 分类浏览: 16个视频/页
- ✅ 搜索功能: 正常返回搜索结果
- ✅ 详情页面: 完整获取视频信息和播放源
- ✅ 播放解析: 正常解析播放地址

## 技术实现

### 使用的Python模块
- **requests**: HTTP请求库
- **pyquery**: jQuery风格HTML解析
- **re**: 正则表达式处理
- **json**: JSON数据处理
- **urllib.parse**: URL编码处理

### URL模式分析
```
主页: https://www.novipnoad.net/
分类页: https://www.novipnoad.net/{category}/
分页: https://www.novipnoad.net/{category}/page/{page}/
详情页: https://www.novipnoad.net/{category}/{id}.html
搜索: https://www.novipnoad.net/?s={keyword}
```

### 分类映射
```python
categories = [
    {'type_name': '电影', 'type_id': 'movie'},
    {'type_name': '港剧', 'type_id': 'tv/hongkong'},
    {'type_name': '台剧', 'type_id': 'tv/taiwan'},
    {'type_name': '欧美剧', 'type_id': 'tv/western'},
    {'type_name': '日剧', 'type_id': 'tv/japan'},
    {'type_name': '韩剧', 'type_id': 'tv/korea'},
    {'type_name': '泰剧', 'type_id': 'tv/thailand'},
    {'type_name': '土耳其剧', 'type_id': 'tv/turkey'},
    {'type_name': '动画', 'type_id': 'anime'},
    {'type_name': '综艺', 'type_id': 'shows'},
    {'type_name': '音乐', 'type_id': 'music'},
    {'type_name': '短片', 'type_id': 'short'},
    {'type_name': '其他', 'type_id': 'other'}
]
```

### CSS选择器
- **视频容器**: `.video-item`
- **视频链接**: `.video-item a`
- **视频标题**: `a[title]` 或 `h3 a`
- **视频图片**: `img[data-original]` 或 `img[src]`
- **播放器**: `.player-container, .video-player, .embed-responsive`

### 特殊处理

#### 1. Brotli压缩处理
网站使用Brotli压缩，需要在请求头中禁用压缩：
```python
headers = {
    'Accept-Encoding': 'identity',  # 禁用压缩
    # ... 其他请求头
}
```

#### 2. 视频ID提取
从URL中提取视频ID的正则表达式：
```python
# 主要模式: /tv/western/150717.html
match = re.search(r'/(\w+/\w+/\d+)\.html', url)
# 备用模式: /movie/150633.html  
match = re.search(r'/(\w+/\d+)\.html', url)
```

#### 3. 标题清理
自动清理标题中的多余文本：
```python
def clean_title(self, title):
    title = re.sub(r'NO视频.*?$', '', title)
    title = re.sub(r'www\.novipnoad\.net.*?$', '', title)
    return title.strip()
```

#### 4. 类型识别
从标题中自动识别视频类型：
```python
if '【美剧】' in title:
    type_name = '美剧'
elif '【韩剧】' in title:
    type_name = '韩剧'
# ... 其他类型
```

## 数据格式

### 视频列表项
```json
{
    "vod_id": "tv/western/150717",
    "vod_name": "【美剧】荒境狂途 (6集全)【官方中字】", 
    "vod_pic": "https://img.novipnoad.net/upload/2025/07/0c45413743848dbb.jpg",
    "vod_year": "2025",
    "vod_remarks": ""
}
```

### 视频详情
```json
{
    "vod_id": "tv/western/150717",
    "vod_name": "【美剧】荒境狂途 (6集全)【官方中字】",
    "vod_pic": "https://img.novipnoad.net/upload/2025/07/0c45413743848dbb.jpg",
    "type_name": "美剧",
    "vod_year": "2025",
    "vod_play_from": "默认播放",
    "vod_play_url": "播放$https://www.novipnoad.net/tv/western/150717.html"
}
```

## 使用说明

### 安装要求
- Python 3.7+
- 已安装依赖包: requests, pyquery

### 使用方法
1. 将插件文件放置在 `plugin/html/` 目录下
2. 在PyramidStore中加载插件
3. 即可使用所有功能

### 测试方法
运行测试脚本验证功能:
```bash
python test_NO视频.py
```

## 注意事项

1. **网络要求**: 需要能够访问 https://www.novipnoad.net/
2. **压缩处理**: 网站使用Brotli压缩，插件已自动处理
3. **请求频率**: 建议控制请求频率，避免被网站限制
4. **User-Agent**: 插件已设置合适的浏览器标识
5. **编码处理**: 自动处理中文编码问题
6. **错误处理**: 包含完善的异常处理机制

## 问题修复记录

### 已修复问题 ✅

#### 1. 视频标题显示问题
**问题描述**: 视频标题包含大量多余信息，如【美剧】、(6集全)、【官方中字】等标识
**修复方案**:
- 增强 `clean_title()` 方法
- 使用正则表达式移除方括号、圆括号、下划线后缀等
- 保留核心标题内容

**修复效果**:
```
修复前: 【美剧】荒境狂途 (6集全)【官方中字】_高清在线观看
修复后: 荒境狂途
```

#### 2. 播放功能问题
**问题描述**: 无法正确解析剧集列表，播放功能不完整
**修复方案**:
- 识别 `.multilink-btn[data-vid]` 播放按钮
- 提取每个剧集的 `data-vid` 参数
- 构建包含vid参数的播放URL
- 改进 `playerContent()` 方法处理vid参数

**修复效果**:
```
修复前: 只有单个播放链接
修复后: 完整的6集剧集列表 (E01-E06)
```

### 当前限制
1. **播放地址解析**: 需要进一步分析AJAX API获取真实播放地址
2. **详细信息**: 导演、演员等信息提取有限

### 优化方向
1. **增强播放解析**: 深入分析播放API，提取真实播放地址
2. **丰富详情信息**: 提取更多视频元信息
3. **缓存机制**: 添加适当的缓存以提高性能

## 更新日志

### v1.1.0 (2025-08-04) - 问题修复版
- ✅ **修复标题显示问题**: 优化标题清理功能，移除【】()等多余标识
- ✅ **改进播放功能**: 正确解析剧集列表，支持多集播放
- ✅ **增强播放解析**: 识别vid参数，支持AJAX播放地址获取
- ✅ **完善错误处理**: 增加更多异常处理和日志记录
- ✅ **优化用户体验**: 标题显示更简洁，播放列表更完整

### v1.0.0 (2025-08-04)
- ✅ 初始版本发布
- ✅ 实现所有基础功能
- ✅ 通过完整功能测试
- ✅ 支持13个分类浏览
- ✅ 处理Brotli压缩问题
- ✅ 自动类型识别和标题清理

## 开发者信息
- 基于PyramidStore框架开发
- 参考模板: plugin/html/泥视频.py
- 严格遵循项目编码规范
- 使用项目指定的依赖模块
- 完整的错误处理和日志记录

## 技术支持
如有问题或建议，请通过以下方式联系：
- 项目仓库: PyramidStore
- 开发框架: 基于Spider基类
- 测试环境: Python 3.11.9
