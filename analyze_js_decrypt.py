# -*- coding: utf-8 -*-
# 分析JavaScript解密函数
import requests
import re
import base64

def get_player_js():
    """获取player.js文件内容"""
    base_url = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Referer': 'http://www.bimiacg11.net/',
    }

    try:
        # 获取player.js文件
        player_js_url = f"{base_url}/static/js/player.js"
        response = requests.get(player_js_url, headers=headers, timeout=10)

        if response.status_code == 200:
            return response.text
        else:
            print(f"获取player.js失败: {response.status_code}")
            return None

    except Exception as e:
        print(f"获取player.js异常: {e}")
        return None

def analyze_base64decode_function(js_content):
    """分析base64decode函数的实现"""
    print("分析base64decode函数:")

    # 查找base64decode函数的完整实现
    patterns = [
        r'function\s+base64decode\s*\([^)]*\)\s*{([^}]+)}',
        r'base64decode\s*=\s*function\s*\([^)]*\)\s*{([^}]+)}',
        r'var\s+base64decode\s*=\s*function\s*\([^)]*\)\s*{([^}]+)}'
    ]

    for i, pattern in enumerate(patterns):
        matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"  模式 {i+1} 找到base64decode函数:")
            for j, match in enumerate(matches):
                print(f"    函数 {j+1} (长度: {len(match)}):")
                print(f"    完整内容: {match}")

                # 分析函数内容
                analyze_function_content(match)
            return matches

    print("  未找到base64decode函数")
    return None

def analyze_function_content(func_content):
    """分析函数内容"""
    print(f"    分析函数内容:")

    # 查找字符替换逻辑
    replace_patterns = [
        r'\.replace\([^)]+\)',
        r'String\.fromCharCode\([^)]+\)',
        r'charAt\([^)]+\)',
        r'charCodeAt\([^)]+\)'
    ]

    for pattern in replace_patterns:
        matches = re.findall(pattern, func_content)
        if matches:
            print(f"      找到操作: {matches}")

def implement_custom_base64decode(encoded_str):
    """实现自定义的base64解码"""
    print(f"\n实现自定义base64解码:")
    print(f"  输入: {encoded_str[:100]}...")

    # 根据分析的JavaScript实现自定义解码
    # 这里需要根据实际的JavaScript函数来实现

    # 尝试多种可能的解码方式
    decode_attempts = []

    # 方法1: 直接base64解码（可能需要特殊处理）
    try:
        # 处理可能的特殊字符
        cleaned = encoded_str.replace(' ', '+').replace('-', '+').replace('_', '/')

        # 添加padding
        while len(cleaned) % 4 != 0:
            cleaned += '='

        decoded_bytes = base64.b64decode(cleaned)

        # 尝试不同的编码
        for encoding in ['utf-8', 'latin-1', 'ascii']:
            try:
                decoded_str = decoded_bytes.decode(encoding)
                decode_attempts.append((f'base64+{encoding}', decoded_str))
            except:
                pass

    except Exception as e:
        decode_attempts.append(('base64解码', f'失败: {e}'))

    # 方法2: 自定义字符映射（如果JavaScript中有特殊映射）
    # 这里需要根据实际的JavaScript代码来实现

    return decode_attempts

def test_decrypt_with_known_url():
    """使用已知的加密URL测试解密"""
    print("=" * 80)
    print("分析JavaScript解密函数")
    print("=" * 80)

    # 1. 获取player.js内容
    print("\n1. 获取player.js内容:")
    js_content = get_player_js()

    if not js_content:
        print("无法获取player.js内容")
        return

    print(f"  player.js大小: {len(js_content)} 字符")

    # 2. 分析base64decode函数
    print(f"\n2. 分析base64decode函数:")
    base64_functions = analyze_base64decode_function(js_content)

    # 3. 测试已知的加密URL
    print(f"\n3. 测试解密已知URL:")
    test_url = "0E7vBXkLJVE22QfJMBebk2xPX5FsfJ0YQ93NcZpLiG6UIS7ePFMh3pAkuFBOc0tuPS4PkbjpTr7nDFwAqQkWPxyQuRlZN39vR1rGwsHyatVkyQGuhxqu/MGHLL7AVOrsC9l6fFvIRReN89X51bzT3JHBqFPvdk2hzGvC/7QQi2n3G4xUsgCU7zexAWHXBOcO"

    decode_results = implement_custom_base64decode(test_url)

    for method, result in decode_results:
        print(f"  {method}: {result[:200]}...")

        # 检查是否是有效的URL
        if isinstance(result, str) and any(indicator in result.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv']):
            print(f"    ✅ 可能是有效URL: {result}")

            # 验证URL
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'http://www.bimiacg11.net/',
                    'Accept': '*/*'
                }

                test_response = requests.head(result, headers=headers, timeout=10)
                print(f"    验证结果: {test_response.status_code}")

                if test_response.status_code == 200:
                    content_type = test_response.headers.get('Content-Type', '')
                    print(f"    Content-Type: {content_type}")

                    if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                        print(f"    🎉 找到有效的视频地址！")
                        return result

            except Exception as e:
                print(f"    验证失败: {e}")

if __name__ == "__main__":
    result = test_decrypt_with_known_url()
    if result:
        print(f"\n🎉 成功解密得到视频地址: {result}")
    else:
        print(f"\n❌ 解密失败，需要进一步分析JavaScript实现")
