#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 ncat22.com 网站结构
"""

import requests
from pyquery import PyQuery as pq
from urllib.parse import quote, unquote
import urllib.parse

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
}

def analyze_search():
    """分析搜索功能"""
    print('=== 分析搜索URL ===')
    search_url = 'https://www.ncat22.com/search?t=7UovjS64Q7A%2Brk5oJJju1g%3D%3D&k=%E4%BD%A0%E5%A5%BD'
    print(f'搜索URL: {search_url}')
    
    # 解析参数
    parsed = urllib.parse.urlparse(search_url)
    params = urllib.parse.parse_qs(parsed.query)
    t_param = params.get('t', ['无'])[0]
    k_param = unquote(params.get('k', ['无'])[0])
    print(f'参数t: {t_param}')
    print(f'参数k: {k_param}')
    
    # 尝试访问搜索页面
    try:
        response = requests.get(search_url, headers=headers, timeout=30)
        data = pq(response.text)
        print(f'\n搜索页面状态码: {response.status_code}')
        print(f'搜索页面标题: {data("title").text()}')
        
        # 查找搜索结果
        results = data('a[href*="/detail/"]')
        print(f'搜索结果数量: {len(results)} 个')
        
        if len(results) > 0:
            print('前3个搜索结果:')
            for i in range(min(3, len(results))):
                result = results.eq(i)
                href = result.attr('href')
                text = result.text().strip()
                print(f'  {i+1}. {text} -> {href}')
                
    except Exception as e:
        print(f'搜索页面访问失败: {e}')

def analyze_category():
    """分析分类页面"""
    print('\n=== 分析电影分类页面 ===')
    try:
        response = requests.get('https://www.ncat22.com/channel/1.html', headers=headers, timeout=30)
        data = pq(response.text)
        
        # 查找包含详情链接的父元素
        detail_links = data('a[href*="/detail/"]')
        print(f'找到详情链接: {len(detail_links)} 个')
        
        if len(detail_links) > 0:
            # 分析第一个详情链接的父元素结构
            first_link = detail_links.eq(0)
            parent = first_link.parent()
            print(f'\n第一个详情链接的父元素:')
            print(f'  标签: {parent[0].tag if len(parent) > 0 else "无"}')
            print(f'  类名: {parent.attr("class")}')
            print(f'  HTML: {parent.outer_html()[:500]}...')
            
            # 查找图片
            img = parent('img')
            if len(img) > 0:
                print(f'  包含图片: {img.attr("src")}')
                print(f'  图片alt: {img.attr("alt")}')
                
            # 分析更多视频项的结构
            print(f'\n分析前5个视频项:')
            for i in range(min(5, len(detail_links))):
                link = detail_links.eq(i)
                href = link.attr('href')
                text = link.text().strip()
                parent = link.parent()
                parent_class = parent.attr('class')
                print(f'  {i+1}. {text} -> {href} (父元素类: {parent_class})')
                
    except Exception as e:
        print(f'分类页面分析失败: {e}')

def analyze_detail():
    """分析详情页面"""
    print('\n=== 分析详情页面 ===')
    detail_url = 'https://www.ncat22.com/detail/297520.html'
    try:
        response = requests.get(detail_url, headers=headers, timeout=30)
        data = pq(response.text)
        print(f'详情页状态码: {response.status_code}')
        print(f'详情页标题: {data("title").text()}')
        
        # 查找播放链接
        play_links = data('a[href*="/play/"]')
        print(f'\n找到播放链接: {len(play_links)} 个')
        if len(play_links) > 0:
            for i in range(min(3, len(play_links))):
                link = play_links.eq(i)
                href = link.attr('href')
                text = link.text().strip()
                print(f'  播放链接{i+1}: {href} - {text}')
                
        # 查找视频信息
        print('\n查找视频信息:')
        info_selectors = ['.info', '.detail', '.content', '.description', '.meta']
        for selector in info_selectors:
            elements = data(selector)
            if len(elements) > 0:
                print(f'  找到 {selector}: {len(elements)} 个')
                
    except Exception as e:
        print(f'详情页面分析失败: {e}')

def analyze_play():
    """分析播放页面"""
    print('\n=== 分析播放页面 ===')
    play_url = 'https://www.ncat22.com/play/297520-35-2219443.html'
    try:
        response = requests.get(play_url, headers=headers, timeout=30)
        data = pq(response.text)
        print(f'播放页面状态码: {response.status_code}')
        print(f'播放页面标题: {data("title").text()}')
        
        # 查找视频播放相关的元素
        video_elements = data('video, iframe, embed, object')
        print(f'找到视频元素: {len(video_elements)} 个')
        
        if len(video_elements) > 0:
            for i, elem in enumerate(video_elements.items()):
                tag_name = elem[0].tag
                src = elem.attr('src')
                print(f'  视频元素{i+1}: {tag_name} - src: {src}')
                
    except Exception as e:
        print(f'播放页面分析失败: {e}')

if __name__ == "__main__":
    analyze_search()
    analyze_category()
    analyze_detail()
    analyze_play()
