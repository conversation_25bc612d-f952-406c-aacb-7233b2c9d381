# -*- coding: utf-8 -*-
# 深入分析NO视频的播放API机制
import requests
import re
import json
import time
from urllib.parse import urlencode, quote
from pyquery import PyQuery as pq

def analyze_play_api():
    """分析NO视频的播放API机制"""
    
    # 测试详情页
    detail_url = 'https://www.novipnoad.net/tv/western/150717.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': 'https://www.novipnoad.net/',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    }
    
    print("=" * 80)
    print("深入分析NO视频播放API机制")
    print("=" * 80)
    
    try:
        # 1. 获取详情页面
        print(f"\n1. 获取详情页面: {detail_url}")
        response = requests.get(detail_url, headers=headers, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code != 200:
            print("获取详情页失败")
            return
        
        content = response.text
        data = pq(content)
        
        # 2. 提取播放相关信息
        print("\n2. 提取播放相关信息...")
        
        # 提取播放密钥
        pkey_match = re.search(r'window\.playInfo\s*=\s*\{\s*pkey\s*:\s*["\']([^"\']+)["\']', content)
        pkey = pkey_match.group(1) if pkey_match else None
        print(f"播放密钥(pkey): {pkey}")
        
        # 提取所有vid
        vid_matches = re.findall(r'data-vid=["\']([^"\']+)["\']', content)
        print(f"找到的vid列表: {vid_matches}")

        # 如果没找到vid，尝试其他模式
        if not vid_matches:
            print("尝试其他vid提取模式...")
            # 保存页面内容用于分析
            with open('novipnoad_detail_debug.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("页面内容已保存到: novipnoad_detail_debug.html")

            # 查找multilink-btn
            data = pq(content)
            multilink_buttons = data('.multilink-btn')
            print(f"找到 {len(multilink_buttons)} 个multilink-btn")

            for i, btn in enumerate(multilink_buttons.items()):
                vid = btn.attr('data-vid')
                text = btn.text().strip()
                print(f"  按钮 {i+1}: text='{text}', data-vid='{vid}'")
                if vid:
                    vid_matches.append(vid)

        if not pkey:
            print("❌ 未找到播放密钥")
            return

        if not vid_matches:
            print("❌ 未找到vid参数，继续其他分析...")
            vid_matches = ['test-vid']  # 使用测试vid继续分析
        
        # 3. 分析JavaScript播放逻辑
        print("\n3. 分析JavaScript播放逻辑...")
        
        # 查找play.js文件
        play_js_match = re.search(r'src=["\']([^"\']*play\.js[^"\']*)["\']', content)
        if play_js_match:
            play_js_url = play_js_match.group(1)
            if not play_js_url.startswith('http'):
                play_js_url = 'https://www.novipnoad.net' + play_js_url
            print(f"播放JS文件: {play_js_url}")
            
            # 获取播放JS文件
            try:
                js_response = requests.get(play_js_url, headers=headers, timeout=10)
                if js_response.status_code == 200:
                    js_content = js_response.text
                    print(f"JS文件大小: {len(js_content)} 字符")
                    
                    # 分析JS中的API调用
                    api_patterns = [
                        r'ajax\s*\(\s*["\']([^"\']+)["\']',
                        r'\.get\s*\(\s*["\']([^"\']+)["\']',
                        r'\.post\s*\(\s*["\']([^"\']+)["\']',
                        r'url\s*:\s*["\']([^"\']+)["\']',
                        r'action\s*:\s*["\']([^"\']+)["\']',
                    ]
                    
                    found_apis = set()
                    for pattern in api_patterns:
                        matches = re.findall(pattern, js_content)
                        for match in matches:
                            if 'ajax' in match or 'api' in match or 'play' in match:
                                found_apis.add(match)
                    
                    print("找到的可能API端点:")
                    for api in found_apis:
                        print(f"  - {api}")
                    
                    # 保存JS文件用于进一步分析
                    with open('novipnoad_play.js', 'w', encoding='utf-8') as f:
                        f.write(js_content)
                    print("播放JS已保存到: novipnoad_play.js")
                    
            except Exception as e:
                print(f"获取JS文件失败: {e}")
        
        # 4. 尝试常见的API端点
        print("\n4. 尝试常见的API端点...")
        
        base_url = 'https://www.novipnoad.net'
        api_endpoints = [
            '/lib/ajax.php',
            '/api/play.php',
            '/ajax/play.php',
            '/play/api.php',
            '/api/video.php',
            '/lib/play.php'
        ]
        
        test_vid = vid_matches[0] if vid_matches else 'ftn-1752837192'
        
        for endpoint in api_endpoints:
            api_url = base_url + endpoint
            print(f"\n测试API端点: {api_url}")
            
            # 尝试不同的请求方法和参数
            test_params = [
                {'action': 'get_play_url', 'vid': test_vid, 'pkey': pkey},
                {'action': 'play', 'vid': test_vid, 'key': pkey},
                {'vid': test_vid, 'pkey': pkey},
                {'v': test_vid, 'k': pkey},
                {'id': test_vid, 'token': pkey},
            ]
            
            for params in test_params:
                try:
                    # GET请求
                    get_url = f"{api_url}?{urlencode(params)}"
                    get_response = requests.get(get_url, headers=headers, timeout=5)
                    if get_response.status_code == 200 and get_response.text.strip():
                        print(f"  GET成功: {get_response.text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(get_response.text)
                            print(f"  JSON解析成功: {json_data}")
                            
                            # 查找可能的播放地址
                            for key in ['url', 'play_url', 'video_url', 'src', 'link']:
                                if key in json_data:
                                    print(f"  🎯 找到播放地址({key}): {json_data[key]}")
                        except:
                            pass
                    
                    # POST请求
                    post_response = requests.post(api_url, data=params, headers=headers, timeout=5)
                    if post_response.status_code == 200 and post_response.text.strip():
                        print(f"  POST成功: {post_response.text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(post_response.text)
                            print(f"  JSON解析成功: {json_data}")
                            
                            # 查找可能的播放地址
                            for key in ['url', 'play_url', 'video_url', 'src', 'link']:
                                if key in json_data:
                                    print(f"  🎯 找到播放地址({key}): {json_data[key]}")
                        except:
                            pass
                            
                except Exception as e:
                    continue
        
        # 5. 分析页面中的隐藏播放信息
        print("\n5. 分析页面中的隐藏播放信息...")
        
        # 查找可能的播放地址模式
        url_patterns = [
            r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/play/[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/video/[^"\'>\s]*)',
            r'["\']url["\']:\s*["\']([^"\']+)["\']',
            r'["\']src["\']:\s*["\']([^"\']+)["\']',
            r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match[0] else match[1]
                if 'http' in match and ('.m3u8' in match or '.mp4' in match or 'play' in match or 'video' in match):
                    found_urls.add(match)
        
        if found_urls:
            print("页面中找到的可能播放地址:")
            for url in found_urls:
                print(f"  🎯 {url}")
        else:
            print("页面中未找到明显的播放地址")
        
        # 6. 分析iframe嵌套
        print("\n6. 分析iframe嵌套...")
        iframes = data('iframe')
        print(f"找到 {len(iframes)} 个iframe")
        
        for i, iframe in enumerate(iframes.items()):
            src = iframe.attr('src')
            if src:
                print(f"  iframe {i+1}: {src}")
                
                # 如果是相对路径，补全
                if src.startswith('/'):
                    src = base_url + src
                elif not src.startswith('http'):
                    continue
                
                # 尝试访问iframe内容
                try:
                    iframe_response = requests.get(src, headers=headers, timeout=10)
                    if iframe_response.status_code == 200:
                        iframe_content = iframe_response.text
                        print(f"    iframe内容大小: {len(iframe_content)} 字符")
                        
                        # 在iframe中查找播放地址
                        for pattern in url_patterns:
                            matches = re.findall(pattern, iframe_content)
                            for match in matches:
                                if isinstance(match, tuple):
                                    match = match[0] if match[0] else match[1]
                                if 'http' in match and ('.m3u8' in match or '.mp4' in match):
                                    print(f"    🎯 iframe中找到播放地址: {match}")
                except Exception as e:
                    print(f"    访问iframe失败: {e}")
        
        print("\n" + "=" * 80)
        print("播放API分析完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_play_api()
