# -*- coding: utf-8 -*-
# 测试加密URL是否可以直接访问
import requests
import base64
from urllib.parse import unquote

def test_encrypted_url():
    """测试加密URL是否可以直接访问"""

    # 从弹幕播放器中提取的加密URL
    encrypted_url = "3pDyrAAwv5vlNi75YTzs3cekGHfffeixTALtjL29AuBaSZSJVAFj+nMUD2DxH7fz8iEbNxJxV2xa9ipCOfdJbnlmZapsk3sroHHZIFcAaEYmiyKTTo5psnockAWd6NvSmzhvzXpZ/8x7QGjHSZHi5MVHs86WBkDl1Cx78OiI//lnxIYSDZKWwleh9K+0BWsE"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Referer': 'http://www.bimiacg11.net/',
        'Origin': 'http://www.bimiacg11.net'
    }

    print("=" * 80)
    print("测试加密URL是否可以直接访问")
    print("=" * 80)

    print(f"\n原始加密URL: {encrypted_url}")

    # 1. 尝试base64解码
    print(f"\n1. 尝试base64解码:")
    try:
        # 添加可能缺失的padding
        padded_url = encrypted_url + '=' * (4 - len(encrypted_url) % 4)
        decoded_bytes = base64.b64decode(padded_url)
        decoded_str = decoded_bytes.decode('utf-8')
        print(f"  Base64解码结果: {decoded_str}")

        # 检查解码结果是否是有效URL
        if decoded_str.startswith('http') or '/' in decoded_str:
            print(f"  解码结果看起来像URL，尝试访问...")

            # 如果是相对路径，构建完整URL
            if not decoded_str.startswith('http'):
                if decoded_str.startswith('/'):
                    test_url = f"http://www.bimiacg11.net{decoded_str}"
                else:
                    test_url = f"http://www.bimiacg11.net/{decoded_str}"
            else:
                test_url = decoded_str

            try:
                response = requests.head(test_url, headers=headers, timeout=10)
                print(f"  访问结果: {response.status_code}")
                print(f"  Content-Type: {response.headers.get('Content-Type', 'unknown')}")
                print(f"  Content-Length: {response.headers.get('Content-Length', 'unknown')}")

                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                        print(f"  ✅ 这是一个有效的视频地址！")
                        return test_url
            except Exception as e:
                print(f"  访问失败: {e}")

    except Exception as e:
        print(f"  Base64解码失败: {e}")

    # 2. 尝试URL解码
    print(f"\n2. 尝试URL解码:")
    try:
        url_decoded = unquote(encrypted_url)
        print(f"  URL解码结果: {url_decoded}")

        if url_decoded != encrypted_url:
            print(f"  URL解码有变化，尝试访问...")
            # 类似的访问逻辑
    except Exception as e:
        print(f"  URL解码失败: {e}")

    # 3. 尝试直接访问加密URL（可能是相对路径）
    print(f"\n3. 尝试直接访问加密URL:")
    test_urls = [
        f"http://www.bimiacg11.net/{encrypted_url}",
        f"http://www.bimiacg11.net/static/{encrypted_url}",
        f"http://www.bimiacg11.net/video/{encrypted_url}",
        encrypted_url  # 如果是完整URL
    ]

    for test_url in test_urls:
        try:
            print(f"  尝试访问: {test_url[:80]}...")
            response = requests.head(test_url, headers=headers, timeout=5)
            print(f"    状态码: {response.status_code}")

            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                print(f"    Content-Type: {content_type}")

                if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                    print(f"    ✅ 找到有效的视频地址！")
                    return test_url

        except Exception as e:
            print(f"    访问失败: {e}")

    # 4. 尝试其他可能的解密方法
    print(f"\n4. 尝试其他解密方法:")

    # 尝试替换特殊字符
    try:
        # 替换URL安全的base64字符
        url_safe_decoded = encrypted_url.replace('-', '+').replace('_', '/')
        padded_url = url_safe_decoded + '=' * (4 - len(url_safe_decoded) % 4)
        decoded_bytes = base64.b64decode(padded_url)
        decoded_str = decoded_bytes.decode('utf-8')
        print(f"  URL安全Base64解码结果: {decoded_str}")

        if decoded_str.startswith('http') or '/' in decoded_str:
            print(f"  尝试访问解码后的URL...")
            # 类似的访问逻辑

    except Exception as e:
        print(f"  URL安全Base64解码失败: {e}")

    return None

if __name__ == "__main__":
    result = test_encrypted_url()
    if result:
        print(f"\n🎉 成功找到可直接播放的视频地址: {result}")
    else:
        print(f"\n❌ 未能找到可直接播放的视频地址")
        print(f"\n💡 建议：这个加密URL可能需要特殊的解密算法或者只能在特定的播放器环境中使用")
