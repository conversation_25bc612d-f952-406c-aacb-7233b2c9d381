# -*- coding: utf-8 -*-
# 分析NO视频真实播放机制
import requests
import re
import json
from pyquery import PyQuery as pq

def analyze_real_play():
    """分析真实的播放机制"""
    
    print("=" * 80)
    print("分析NO视频真实播放机制")
    print("=" * 80)
    
    # 测试URL
    detail_url = 'https://www.novipnoad.net/tv/western/150717.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': 'https://www.novipnoad.net/',
    }
    
    try:
        # 1. 获取详情页面
        print(f"\n1. 获取详情页面...")
        response = requests.get(detail_url, headers=headers, timeout=15)
        content = response.text
        data = pq(content)
        
        # 2. 分析播放器容器
        print(f"\n2. 分析播放器容器...")
        
        # 查找播放器相关的div
        player_containers = [
            '#player-embed', '.player-embed', '#player', '.player',
            '.video-player', '.embed-responsive', '.player-container'
        ]
        
        for container in player_containers:
            elements = data(container)
            if len(elements) > 0:
                print(f"找到播放器容器: {container}")
                for i, elem in enumerate(elements.items()):
                    html = str(elem)[:300]
                    print(f"  容器{i+1}: {html}...")
        
        # 3. 分析JavaScript中的播放逻辑
        print(f"\n3. 分析JavaScript播放逻辑...")
        
        # 查找所有script标签
        scripts = data('script')
        print(f"找到 {len(scripts)} 个script标签")
        
        # 分析包含播放相关代码的script
        play_related_patterns = [
            r'function\s+play\s*\(',
            r'\.play\s*\(',
            r'video\s*=',
            r'player\s*=',
            r'src\s*=',
            r'ajax\s*\(',
            r'\.get\s*\(',
            r'\.post\s*\(',
        ]
        
        for i, script in enumerate(scripts.items()):
            script_content = script.text()
            if script_content and len(script_content) > 100:
                # 检查是否包含播放相关代码
                has_play_code = any(re.search(pattern, script_content, re.IGNORECASE) 
                                  for pattern in play_related_patterns)
                
                if has_play_code:
                    print(f"\n找到播放相关脚本 {i+1}:")
                    print(f"脚本长度: {len(script_content)} 字符")
                    
                    # 查找函数定义
                    functions = re.findall(r'function\s+(\w+)\s*\([^)]*\)\s*\{', script_content)
                    if functions:
                        print(f"函数列表: {functions}")
                    
                    # 查找AJAX调用
                    ajax_calls = re.findall(r'ajax\s*\(\s*["\']([^"\']+)["\']', script_content)
                    if ajax_calls:
                        print(f"AJAX调用: {ajax_calls}")
                    
                    # 查找URL构建
                    url_patterns = re.findall(r'["\']([^"\']*(?:ajax|play|video|api)[^"\']*)["\']', script_content)
                    if url_patterns:
                        print(f"URL模式: {url_patterns}")
                    
                    # 保存脚本用于详细分析
                    with open(f'play_script_{i+1}.js', 'w', encoding='utf-8') as f:
                        f.write(script_content)
                    print(f"脚本已保存到: play_script_{i+1}.js")
        
        # 4. 查找可能的播放地址模式
        print(f"\n4. 查找播放地址模式...")
        
        # 在整个页面中搜索可能的播放地址
        url_patterns = [
            r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/hls/[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/video/[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/stream/[^"\'>\s]*)',
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                found_urls.add(match)
        
        if found_urls:
            print("页面中找到的可能播放地址:")
            for url in found_urls:
                print(f"  🎯 {url}")
        else:
            print("页面中未找到明显的播放地址")
        
        # 5. 分析可能的第三方播放器
        print(f"\n5. 分析第三方播放器...")
        
        # 查找可能的第三方播放器域名
        third_party_patterns = [
            r'(https?://[^"\'>\s]*(?:player|video|stream|cdn)[^"\'>\s]*)',
            r'src=["\']([^"\']+)["\']',
        ]
        
        third_party_urls = set()
        for pattern in third_party_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                if 'http' in match and 'novipnoad.net' not in match:
                    third_party_urls.add(match)
        
        if third_party_urls:
            print("找到的第三方播放器:")
            for url in third_party_urls:
                print(f"  🔗 {url}")
        
        # 6. 尝试模拟点击播放按钮
        print(f"\n6. 模拟点击播放按钮...")
        
        # 获取第一个播放按钮的信息
        play_buttons = data('.multilink-btn[data-vid]')
        if len(play_buttons) > 0:
            first_button = play_buttons.eq(0)
            vid = first_button.attr('data-vid')
            episode_name = first_button.text().strip()
            
            print(f"模拟点击: {episode_name} (vid: {vid})")
            
            # 提取pkey
            pkey_match = re.search(r'window\.playInfo\s*=\s*\{\s*pkey\s*:\s*["\']([^"\']+)["\']', content)
            if pkey_match:
                pkey = pkey_match.group(1)
                print(f"使用pkey: {pkey}")
                
                # 尝试构建播放请求
                play_url_with_vid = f"{detail_url}?vid={vid}"
                print(f"构建播放URL: {play_url_with_vid}")
                
                # 访问带vid参数的页面
                vid_headers = headers.copy()
                vid_headers['Referer'] = detail_url
                
                vid_response = requests.get(play_url_with_vid, headers=vid_headers, timeout=15)
                vid_content = vid_response.text
                
                print(f"带vid页面状态: {vid_response.status_code}")
                print(f"带vid页面大小: {len(vid_content)} 字符")
                
                # 在带vid的页面中查找播放地址
                for pattern in url_patterns:
                    matches = re.findall(pattern, vid_content)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]
                        if match not in found_urls:
                            print(f"  🎯 新发现的播放地址: {match}")
                
                # 保存带vid的页面
                with open('vid_page.html', 'w', encoding='utf-8') as f:
                    f.write(vid_content)
                print("带vid页面已保存到: vid_page.html")
        
        print("\n" + "=" * 80)
        print("真实播放机制分析完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_real_play()
