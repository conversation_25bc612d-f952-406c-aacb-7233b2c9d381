# -*- coding: utf-8 -*-
# 正确保存HTML内容
import requests

def save_html():
    target_url = 'https://www.novipnoad.net/'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity'  # 禁用压缩
    }
    
    try:
        response = requests.get(target_url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        print(f"前100字符: {response.text[:100]}")
        
        # 保存HTML
        with open('novipnoad_correct.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print("HTML已正确保存到 novipnoad_correct.html")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    save_html()
