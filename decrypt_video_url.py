# -*- coding: utf-8 -*-
# 深度解密视频播放地址
import requests
import re
import json
import base64
from urllib.parse import urljoin, unquote, quote
import binascii

def custom_base64_decode(encoded_str):
    """自定义base64解码，处理各种可能的编码格式"""
    decode_methods = []

    # 方法1: 标准base64解码
    try:
        # 添加padding
        padded = encoded_str + '=' * (4 - len(encoded_str) % 4)
        decoded = base64.b64decode(padded).decode('utf-8')
        decode_methods.append(('标准base64', decoded))
    except Exception as e:
        decode_methods.append(('标准base64', f'失败: {e}'))

    # 方法2: URL安全base64解码
    try:
        # 替换URL安全字符
        url_safe = encoded_str.replace('-', '+').replace('_', '/')
        padded = url_safe + '=' * (4 - len(url_safe) % 4)
        decoded = base64.b64decode(padded).decode('utf-8')
        decode_methods.append(('URL安全base64', decoded))
    except Exception as e:
        decode_methods.append(('URL安全base64', f'失败: {e}'))

    # 方法3: 尝试不同的字符集替换
    try:
        # 一些网站使用自定义的base64字符集
        custom_chars = encoded_str.replace('_', '/').replace('-', '+')
        padded = custom_chars + '=' * (4 - len(custom_chars) % 4)
        decoded = base64.b64decode(padded).decode('utf-8')
        decode_methods.append(('自定义字符集', decoded))
    except Exception as e:
        decode_methods.append(('自定义字符集', f'失败: {e}'))

    # 方法4: 尝试latin-1编码
    try:
        padded = encoded_str + '=' * (4 - len(encoded_str) % 4)
        decoded_bytes = base64.b64decode(padded)
        decoded = decoded_bytes.decode('latin-1')
        decode_methods.append(('latin-1编码', decoded))
    except Exception as e:
        decode_methods.append(('latin-1编码', f'失败: {e}'))

    return decode_methods

def analyze_js_decrypt_functions(js_content):
    """分析JavaScript中的解密函数"""
    print("\n分析JavaScript解密函数:")

    # 查找base64decode函数的完整实现
    base64_patterns = [
        r'function\s+base64decode\s*\([^)]*\)\s*{([^}]+)}',
        r'base64decode\s*=\s*function[^}]+}',
        r'var\s+base64decode\s*=\s*function[^}]+}'
    ]

    for pattern in base64_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
        if matches:
            for i, match in enumerate(matches):
                print(f"  base64decode函数 {i+1}: {match[:300]}...")

    # 查找自定义解密逻辑
    decrypt_patterns = [
        r'if\s*\(\s*encrypt\s*==\s*["\']?(\d+)["\']?\s*\)\s*{([^}]+)}',
        r'switch\s*\(\s*encrypt\s*\)\s*{([^}]+)}',
        r'player_data\.url\s*=\s*([^;]+);'
    ]

    for pattern in decrypt_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"  解密逻辑: {matches}")

def get_latest_encrypted_url():
    """获取最新的加密URL"""
    base_url = 'http://www.bimiacg11.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    print("获取最新的加密URL...")

    try:
        # 1. 获取播放页面
        play_url = f"{base_url}/bangumi/38328/play/1/1/"
        print(f"  访问播放页面: {play_url}")
        response = requests.get(play_url, headers=headers, timeout=10)
        print(f"  播放页面状态码: {response.status_code}")

        if response.status_code == 200:
            content = response.text
            print(f"  播放页面内容长度: {len(content)}")

            # 提取player_aaaa变量
            player_patterns = [
                r'var\s+player_aaaa\s*=\s*({.*?});',
                r'player_aaaa\s*=\s*({.*?});',
                r'var\s+player_aaaa\s*=\s*({[^}]+})',
                r'player_aaaa\s*=\s*({[^}]+})'
            ]

            player_found = False
            for i, pattern in enumerate(player_patterns):
                player_match = re.search(pattern, content, re.DOTALL)
                if player_match:
                    player_found = True
                    player_json_str = player_match.group(1)
                    print(f"  使用模式 {i+1} 找到player_aaaa")

                    # 提取关键信息
                    encrypt_match = re.search(r'"encrypt"[:\s]*(\d+)', player_json_str)
                    url_match = re.search(r'"url"[:\s]*"([^"]+)"', player_json_str)

                    encrypt_type = encrypt_match.group(1) if encrypt_match else '0'
                    encrypted_url = url_match.group(1) if url_match else None

                    print(f"  encrypt类型: {encrypt_type}")
                    print(f"  加密URL长度: {len(encrypted_url) if encrypted_url else 0}")

                    if encrypted_url:
                        print(f"  找到加密URL: {encrypted_url[:100]}...")

                        # 2. 获取弹幕播放器内容
                        danmu_url = f"{base_url}/static/danmu/qy.php?url={encrypted_url}"
                        print(f"  访问弹幕播放器: {danmu_url[:80]}...")
                        danmu_response = requests.get(danmu_url, headers=headers, timeout=10)
                        print(f"  弹幕播放器状态码: {danmu_response.status_code}")

                        if danmu_response.status_code == 200:
                            danmu_content = danmu_response.text
                            print(f"  弹幕播放器内容长度: {len(danmu_content)}")

                            # 提取video标签中的source URL
                            video_pattern = r'<source[^>]+src=["\']([^"\']+)["\'][^>]*>'
                            video_match = re.search(video_pattern, danmu_content)

                            if video_match:
                                source_url = video_match.group(1)
                                print(f"  找到source URL: {source_url[:100]}...")

                                return {
                                    'original_encrypted_url': encrypted_url,
                                    'encrypt_type': encrypt_type,
                                    'source_url': source_url,
                                    'danmu_content': danmu_content
                                }
                            else:
                                print(f"  未找到source标签")
                        else:
                            print(f"  弹幕播放器访问失败")
                    break

            if not player_found:
                print(f"  未找到player_aaaa变量")
                # 查找其他可能的播放相关变量
                other_patterns = [
                    r'var\s+(\w*player\w*)\s*=\s*{[^}]+}',
                    r'(\w*url\w*)\s*=\s*["\']([^"\']+)["\']'
                ]

                for pattern in other_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"    找到其他变量: {matches[:3]}")
        else:
            print(f"  播放页面访问失败")

    except Exception as e:
        print(f"获取加密URL失败: {e}")
        import traceback
        traceback.print_exc()

    return None

def deep_decrypt_analysis():
    """深度解密分析"""
    print("=" * 80)
    print("深度解密视频播放地址分析")
    print("=" * 80)

    # 1. 获取最新的加密数据
    data = get_latest_encrypted_url()
    if not data:
        print("无法获取加密数据")
        return None

    source_url = data['source_url']
    encrypt_type = data['encrypt_type']

    print(f"\n开始解密分析:")
    print(f"  原始source URL: {source_url}")
    print(f"  encrypt类型: {encrypt_type}")

    # 2. 尝试各种解密方法
    print(f"\n尝试解密方法:")

    decode_results = custom_base64_decode(source_url)

    for method, result in decode_results:
        print(f"\n  {method}:")
        if not result.startswith('失败'):
            print(f"    解码结果: {result[:200]}...")

            # 检查解码结果是否是有效URL
            if any(indicator in result.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv', '/']):
                print(f"    ✅ 可能是有效URL，尝试验证...")

                # 构建完整URL进行验证
                test_urls = []
                if result.startswith('http'):
                    test_urls.append(result)
                elif result.startswith('/'):
                    test_urls.append(f"http://www.bimiacg11.net{result}")
                else:
                    test_urls.extend([
                        f"http://www.bimiacg11.net/{result}",
                        f"https://{result}",
                        result
                    ])

                for test_url in test_urls:
                    try:
                        print(f"      验证URL: {test_url[:80]}...")
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Referer': 'http://www.bimiacg11.net/',
                            'Accept': '*/*'
                        }

                        test_response = requests.head(test_url, headers=headers, timeout=10)
                        print(f"        状态码: {test_response.status_code}")

                        if test_response.status_code == 200:
                            content_type = test_response.headers.get('Content-Type', '')
                            content_length = test_response.headers.get('Content-Length', '')
                            print(f"        Content-Type: {content_type}")
                            print(f"        Content-Length: {content_length}")

                            # 检查是否是视频文件
                            if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                                print(f"        🎉 找到有效的视频地址！")
                                return test_url

                    except Exception as e:
                        print(f"        验证失败: {e}")
        else:
            print(f"    {result}")

    # 3. 分析JavaScript解密函数
    print(f"\n分析弹幕播放器中的JavaScript:")
    danmu_content = data['danmu_content']

    # 查找可能的解密相关代码
    js_patterns = [
        r'var\s+url\s*=\s*["\']([^"\']+)["\'];',
        r'hls\.loadSource\(["\']([^"\']+)["\']',
        r'video\.src\s*=\s*["\']([^"\']+)["\']',
        r'source\.src\s*=\s*["\']([^"\']+)["\']'
    ]

    for pattern in js_patterns:
        matches = re.findall(pattern, danmu_content)
        if matches:
            print(f"  JavaScript模式 '{pattern}' 找到: {matches}")

    return None

if __name__ == "__main__":
    result = deep_decrypt_analysis()
    if result:
        print(f"\n🎉 成功解密得到视频地址: {result}")
    else:
        print(f"\n❌ 解密失败，需要进一步分析")
