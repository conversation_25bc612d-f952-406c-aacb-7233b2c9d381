# -*- coding: utf-8 -*-
# 深度HTML分析 - 查找真实播放链接
import requests
import re
import json
import base64
import urllib.parse
from pyquery import PyQuery as pq

def deep_html_analysis():
    """深度分析HTML内容，查找真实播放链接"""
    
    print("=" * 80)
    print("深度HTML分析 - 查找真实播放链接")
    print("=" * 80)
    
    # 目标播放页面
    play_url = 'https://www.novipnoad.net/tv/western/150717.html?vid=ftn-1752837192'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': 'https://www.novipnoad.net/tv/western/150717.html',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    }
    
    try:
        # 1. 下载完整HTML内容
        print(f"\n1. 下载播放页面: {play_url}")
        response = requests.get(play_url, headers=headers, timeout=20)
        print(f"状态码: {response.status_code}")
        print(f"页面大小: {len(response.text)} 字符")
        
        if response.status_code != 200:
            print("下载失败")
            return
        
        html_content = response.text
        data = pq(html_content)
        
        # 保存完整HTML用于分析
        with open('play_page_full.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("完整HTML已保存到: play_page_full.html")
        
        # 2. 搜索所有视频格式URL
        print(f"\n2. 搜索视频格式URL...")
        
        video_patterns = [
            r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.flv[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.avi[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.mkv[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.ts[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/hls/[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/stream/[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+/video/[^"\'>\s]*)',
        ]
        
        found_video_urls = set()
        for pattern in video_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                found_video_urls.add(match)
        
        if found_video_urls:
            print("找到的视频URL:")
            for url in found_video_urls:
                print(f"  🎯 {url}")
        else:
            print("未找到明显的视频URL")
        
        # 3. 分析JavaScript播放器配置
        print(f"\n3. 分析JavaScript播放器配置...")
        
        # 查找播放器配置模式
        player_config_patterns = [
            r'video\s*:\s*["\']([^"\']+)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'source\s*:\s*["\']([^"\']+)["\']',
            r'playlist\s*:\s*["\']([^"\']+)["\']',
            r'["\']url["\']:\s*["\']([^"\']+)["\']',
            r'["\']src["\']:\s*["\']([^"\']+)["\']',
            r'["\']file["\']:\s*["\']([^"\']+)["\']',
        ]
        
        player_urls = set()
        for pattern in player_config_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if 'http' in match and ('.m3u8' in match or '.mp4' in match or 'video' in match):
                    player_urls.add(match)
        
        if player_urls:
            print("播放器配置中的URL:")
            for url in player_urls:
                print(f"  🎯 {url}")
        
        # 4. 分析iframe嵌套
        print(f"\n4. 分析iframe嵌套...")
        
        iframes = data('iframe')
        print(f"找到 {len(iframes)} 个iframe")
        
        for i, iframe in enumerate(iframes.items()):
            src = iframe.attr('src')
            if src:
                print(f"  iframe {i+1}: {src}")
                
                # 访问iframe内容
                if src.startswith('/'):
                    iframe_url = 'https://www.novipnoad.net' + src
                elif src.startswith('//'):
                    iframe_url = 'https:' + src
                elif src.startswith('http'):
                    iframe_url = src
                else:
                    continue
                
                try:
                    iframe_response = requests.get(iframe_url, headers=headers, timeout=10)
                    if iframe_response.status_code == 200:
                        iframe_content = iframe_response.text
                        print(f"    iframe内容大小: {len(iframe_content)} 字符")
                        
                        # 在iframe中搜索播放地址
                        for pattern in video_patterns:
                            matches = re.findall(pattern, iframe_content, re.IGNORECASE)
                            for match in matches:
                                if isinstance(match, tuple):
                                    match = match[0]
                                print(f"    🎯 iframe中的视频URL: {match}")
                        
                        # 保存iframe内容
                        with open(f'iframe_{i+1}.html', 'w', encoding='utf-8') as f:
                            f.write(iframe_content)
                        print(f"    iframe内容已保存到: iframe_{i+1}.html")
                        
                except Exception as e:
                    print(f"    访问iframe失败: {e}")
        
        # 5. 分析base64编码内容
        print(f"\n5. 分析base64编码内容...")
        
        # 查找可能的base64编码
        base64_patterns = [
            r'["\']([A-Za-z0-9+/]{20,}={0,2})["\']',
            r'data:([^"\'>\s]+)',
            r'btoa\(["\']([^"\']+)["\']',
            r'atob\(["\']([^"\']+)["\']',
        ]
        
        for pattern in base64_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                if len(match) > 20:  # 只处理较长的字符串
                    try:
                        # 尝试base64解码
                        decoded = base64.b64decode(match + '==').decode('utf-8', errors='ignore')
                        if 'http' in decoded and ('.m3u8' in decoded or '.mp4' in decoded):
                            print(f"  🎯 base64解码发现播放地址: {decoded}")
                    except:
                        pass
        
        # 6. 分析JavaScript函数和变量
        print(f"\n6. 分析JavaScript函数和变量...")
        
        # 查找可能包含播放地址的JavaScript变量
        js_var_patterns = [
            r'var\s+(\w*(?:url|src|video|play|stream)\w*)\s*=\s*["\']([^"\']+)["\']',
            r'let\s+(\w*(?:url|src|video|play|stream)\w*)\s*=\s*["\']([^"\']+)["\']',
            r'const\s+(\w*(?:url|src|video|play|stream)\w*)\s*=\s*["\']([^"\']+)["\']',
            r'(\w*(?:url|src|video|play|stream)\w*)\s*=\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in js_var_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for var_name, var_value in matches:
                if 'http' in var_value and ('.m3u8' in var_value or '.mp4' in var_value or 'video' in var_value):
                    print(f"  🎯 JavaScript变量 {var_name}: {var_value}")
        
        # 7. 分析AJAX请求
        print(f"\n7. 分析AJAX请求...")
        
        # 查找AJAX请求模式
        ajax_patterns = [
            r'ajax\s*\(\s*["\']([^"\']+)["\']',
            r'\.get\s*\(\s*["\']([^"\']+)["\']',
            r'\.post\s*\(\s*["\']([^"\']+)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*?open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']',
        ]
        
        ajax_urls = set()
        for pattern in ajax_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                if 'ajax' in match or 'api' in match or 'play' in match or 'video' in match:
                    ajax_urls.add(match)
        
        if ajax_urls:
            print("找到的AJAX请求URL:")
            for url in ajax_urls:
                print(f"  🔗 {url}")
        
        # 8. 分析特定播放器库
        print(f"\n8. 分析播放器库...")
        
        # 查找常见播放器库的配置
        player_libraries = [
            'video.js', 'videojs', 'dplayer', 'jwplayer', 'plyr', 'hls.js', 'flv.js'
        ]
        
        for lib in player_libraries:
            if lib.lower() in html_content.lower():
                print(f"  发现播放器库: {lib}")
                
                # 查找该库的配置
                lib_config_pattern = rf'{lib}.*?(\{{[^}}]*\}})'
                matches = re.findall(lib_config_pattern, html_content, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    print(f"    配置: {match[:200]}...")
        
        # 9. 分析URL解码
        print(f"\n9. 分析URL编码内容...")
        
        # 查找URL编码的内容
        encoded_patterns = [
            r'decodeURIComponent\(["\']([^"\']+)["\']',
            r'unescape\(["\']([^"\']+)["\']',
            r'(%[0-9A-Fa-f]{2}){3,}',
        ]
        
        for pattern in encoded_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                try:
                    decoded = urllib.parse.unquote(match)
                    if 'http' in decoded and ('.m3u8' in decoded or '.mp4' in decoded):
                        print(f"  🎯 URL解码发现播放地址: {decoded}")
                except:
                    pass
        
        # 10. 生成分析报告
        print(f"\n10. 生成分析报告...")
        
        analysis_report = {
            'video_urls_found': len(found_video_urls),
            'player_urls_found': len(player_urls),
            'iframes_found': len(iframes),
            'ajax_urls_found': len(ajax_urls),
            'all_video_urls': list(found_video_urls),
            'all_player_urls': list(player_urls),
            'all_ajax_urls': list(ajax_urls)
        }
        
        with open('analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        
        print("分析报告已保存到: analysis_report.json")
        print(f"总计发现 {len(found_video_urls)} 个视频URL")
        print(f"总计发现 {len(player_urls)} 个播放器配置URL")
        print(f"总计发现 {len(ajax_urls)} 个AJAX URL")
        
        print("\n" + "=" * 80)
        print("深度HTML分析完成")
        print("=" * 80)
        
        return analysis_report
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    deep_html_analysis()
