# -*- coding: utf-8 -*-
# 实现正确的解密算法
import requests
import re
import base64
from urllib.parse import unquote

def custom_base64_decode(encoded_str):
    """实现网站的自定义base64解码"""
    # 网站使用的base64字符表
    base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

    # 创建解码映射表（与JavaScript中的base64DecodeChars对应）
    decode_chars = [-1] * 256
    for i, char in enumerate(base64_chars):
        decode_chars[ord(char)] = i

    # 实现解码逻辑（基于JavaScript的base64decode函数）
    length = len(encoded_str)
    i = 0
    out = ""

    while i < length:
        # 获取第一个有效字符
        c1 = -1
        while i < length and c1 == -1:
            c1 = decode_chars[ord(encoded_str[i]) & 0xff]
            i += 1
        if c1 == -1:
            break

        # 获取第二个有效字符
        c2 = -1
        while i < length and c2 == -1:
            c2 = decode_chars[ord(encoded_str[i]) & 0xff]
            i += 1
        if c2 == -1:
            break

        out += chr((c1 << 2) | ((c2 & 0x30) >> 4))

        # 获取第三个字符
        if i < length:
            c3 = ord(encoded_str[i]) & 0xff
            i += 1
            if c3 == 61:  # '=' 字符
                return out
            c3 = decode_chars[c3]
            while i < length and c3 == -1:
                if i < length:
                    c3 = decode_chars[ord(encoded_str[i]) & 0xff]
                    i += 1
            if c3 == -1:
                break
            out += chr(((c2 & 0x0F) << 4) | ((c3 & 0x3C) >> 2))

        # 获取第四个字符
        if i < length:
            c4 = ord(encoded_str[i]) & 0xff
            i += 1
            if c4 == 61:  # '=' 字符
                return out
            c4 = decode_chars[c4]
            while i < length and c4 == -1:
                if i < length:
                    c4 = decode_chars[ord(encoded_str[i]) & 0xff]
                    i += 1
            if c4 == -1:
                break
            out += chr(((c3 & 0x03) << 6) | c4)

    return out

def decrypt_video_url(encrypted_url):
    """解密视频URL"""
    print(f"开始解密URL: {encrypted_url[:100]}...")

    try:
        # 第一步：自定义base64解码
        base64_decoded = custom_base64_decode(encrypted_url)
        print(f"Base64解码结果: {base64_decoded[:200]}...")

        # 第二步：unescape解码
        final_url = unquote(base64_decoded)
        print(f"Unescape解码结果: {final_url}")

        return final_url

    except Exception as e:
        print(f"解密失败: {e}")
        return None

def test_decrypt_and_verify():
    """测试解密并验证结果"""
    print("=" * 80)
    print("实现正确的解密算法测试")
    print("=" * 80)

    # 使用最新获取的加密URL
    test_url = "0E7vBXkLJVE22QfJMBebk2xPX5FsfJ0YQ93NcZpLiG6UIS7ePFMh3pAkuFBOc0tuPS4PkbjpTr7nDFwAqQkWPxyQuRlZN39vR1rGwsHyatVkyQGuhxqu/MGHLL7AVOrsC9l6fFvIRReN89X51bzT3JHBqFPvdk2hzGvC/7QQi2n3G4xUsgCU7zexAWHXBOcO"

    # 解密URL
    decrypted_url = decrypt_video_url(test_url)

    if decrypted_url:
        print(f"\n解密成功！")
        print(f"解密后的URL: {decrypted_url}")

        # 验证URL是否有效
        print(f"\n验证URL有效性:")

        # 构建完整URL（如果是相对路径）
        if decrypted_url.startswith('http'):
            test_video_url = decrypted_url
        elif decrypted_url.startswith('/'):
            test_video_url = f"http://www.bimiacg11.net{decrypted_url}"
        else:
            test_video_url = f"http://www.bimiacg11.net/{decrypted_url}"

        print(f"测试URL: {test_video_url}")

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'http://www.bimiacg11.net/',
                'Accept': '*/*'
            }

            response = requests.head(test_video_url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                content_length = response.headers.get('Content-Length', '')

                print(f"Content-Type: {content_type}")
                print(f"Content-Length: {content_length}")

                # 检查是否是视频文件
                if any(vtype in content_type.lower() for vtype in ['video', 'application/vnd.apple.mpegurl', 'application/x-mpegurl']):
                    print(f"🎉 成功！这是一个有效的视频地址！")
                    return test_video_url
                else:
                    print(f"⚠️ 不是视频文件，可能是其他类型的内容")
            else:
                print(f"❌ URL访问失败")

        except Exception as e:
            print(f"验证失败: {e}")

    else:
        print(f"❌ 解密失败")

    return None

if __name__ == "__main__":
    result = test_decrypt_and_verify()
    if result:
        print(f"\n🎉 最终成功获得可直接播放的视频地址: {result}")
    else:
        print(f"\n❌ 未能获得有效的视频地址")
