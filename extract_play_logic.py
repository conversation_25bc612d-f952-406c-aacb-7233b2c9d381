# -*- coding: utf-8 -*-
# 从混淆的JavaScript中提取播放逻辑
import requests
import re
import json
import base64
import urllib.parse

def extract_play_logic():
    """从混淆的JavaScript中提取播放逻辑"""
    
    print("=" * 80)
    print("从混淆的JavaScript中提取播放逻辑")
    print("=" * 80)
    
    try:
        # 读取混淆的JavaScript文件
        with open('play_original.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print(f"JavaScript文件大小: {len(js_content)} 字符")
        
        # 1. 查找可能的API端点
        print(f"\n1. 查找API端点...")
        
        # 在混淆代码中查找可能的URL模式
        url_patterns = [
            r'["\']([^"\']*ajax[^"\']*)["\']',
            r'["\']([^"\']*api[^"\']*)["\']',
            r'["\']([^"\']*play[^"\']*)["\']',
            r'["\']([^"\']*video[^"\']*)["\']',
            r'["\']([^"\']*lib/[^"\']*)["\']',
            r'["\']([^"\']*\.php[^"\']*)["\']',
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 5 and ('ajax' in match or 'api' in match or 'play' in match or '.php' in match):
                    found_urls.add(match)
        
        if found_urls:
            print("找到的可能API端点:")
            for url in found_urls:
                print(f"  🔗 {url}")
        
        # 2. 查找参数名称
        print(f"\n2. 查找参数名称...")
        
        param_patterns = [
            r'["\']([^"\']*vid[^"\']*)["\']',
            r'["\']([^"\']*pkey[^"\']*)["\']',
            r'["\']([^"\']*action[^"\']*)["\']',
            r'["\']([^"\']*token[^"\']*)["\']',
            r'["\']([^"\']*key[^"\']*)["\']',
        ]
        
        found_params = set()
        for pattern in param_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 2 and len(match) < 20:
                    found_params.add(match)
        
        if found_params:
            print("找到的可能参数:")
            for param in found_params:
                print(f"  📋 {param}")
        
        # 3. 查找字符串数组
        print(f"\n3. 分析字符串数组...")
        
        # 查找可能包含解码字符串的数组
        array_pattern = r'\[(["\'][^"\']+["\'][,\s]*){10,}\]'
        array_matches = re.findall(array_pattern, js_content)
        
        print(f"找到 {len(array_matches)} 个可能的字符串数组")
        
        # 4. 查找解码函数
        print(f"\n4. 查找解码函数...")
        
        # 查找可能的解码函数调用
        decode_patterns = [
            r'atob\s*\([^)]+\)',
            r'decodeURIComponent\s*\([^)]+\)',
            r'unescape\s*\([^)]+\)',
            r'JSON\.parse\s*\([^)]+\)',
        ]
        
        for pattern in decode_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"  发现解码函数: {pattern} ({len(matches)}次)")
        
        # 5. 尝试提取关键字符串
        print(f"\n5. 提取关键字符串...")
        
        # 查找可能的关键字符串
        key_strings = [
            'ajax', 'post', 'get', 'url', 'src', 'video', 'play', 'stream',
            'm3u8', 'mp4', 'flv', 'hls', 'dash', 'embed', 'iframe'
        ]
        
        for key_string in key_strings:
            # 查找包含关键字符串的上下文
            pattern = rf'.{{0,50}}{key_string}.{{0,50}}'
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                print(f"\n关键字 '{key_string}' 的上下文:")
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    print(f"  {i+1}. ...{match}...")
        
        # 6. 分析可能的播放函数
        print(f"\n6. 分析播放函数...")
        
        # 查找可能的函数定义
        function_patterns = [
            r'function\s+(\w*play\w*)\s*\([^)]*\)',
            r'function\s+(\w*video\w*)\s*\([^)]*\)',
            r'function\s+(\w*load\w*)\s*\([^)]*\)',
            r'(\w*play\w*)\s*:\s*function',
            r'(\w*video\w*)\s*:\s*function',
        ]
        
        found_functions = set()
        for pattern in function_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 3:
                    found_functions.add(match)
        
        if found_functions:
            print("找到的可能播放函数:")
            for func in found_functions:
                print(f"  🔧 {func}")
        
        # 7. 尝试反混淆部分代码
        print(f"\n7. 尝试反混淆...")
        
        # 查找可能的字符串替换模式
        replace_patterns = [
            r'replace\s*\(\s*["\']([^"\']+)["\'],\s*["\']([^"\']*)["\']',
            r'split\s*\(\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in replace_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"字符串操作: {matches[:3]}")  # 只显示前3个
        
        # 8. 生成分析报告
        print(f"\n8. 生成分析报告...")
        
        analysis_report = {
            'file_size': len(js_content),
            'api_endpoints': list(found_urls),
            'parameters': list(found_params),
            'functions': list(found_functions),
            'is_heavily_obfuscated': True,
            'contains_decode_functions': any(re.search(pattern, js_content) for pattern in decode_patterns)
        }
        
        with open('js_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        
        print("JavaScript分析报告已保存到: js_analysis_report.json")
        
        # 9. 基于分析结果的建议
        print(f"\n9. 分析结论和建议...")
        
        print("分析结论:")
        print("1. JavaScript代码经过严重混淆，包含大量编码/解码函数")
        print("2. 代码使用了复杂的字符串数组和解码机制")
        print("3. 真实的播放逻辑被深度隐藏在混淆代码中")
        print("4. 需要JavaScript执行环境才能获取真实播放地址")
        
        print("\n建议的解决方案:")
        print("1. 使用浏览器自动化工具(如Selenium)模拟真实浏览器环境")
        print("2. 在PyramidStore插件中返回带vid参数的页面URL，让播放器进一步解析")
        print("3. 设置parse=1，让系统尝试解析页面中的播放器")
        print("4. 考虑使用无头浏览器执行JavaScript获取真实播放地址")
        
        return analysis_report
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    extract_play_logic()
