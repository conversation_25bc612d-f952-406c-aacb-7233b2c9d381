# -*- coding: utf-8 -*-
# 网站结构分析脚本 - https://www.novipnoad.net/
import requests
import re
import json
from pyquery import PyQuery as pq
from urllib.parse import urljoin, urlparse

def analyze_website():
    """分析网站结构"""
    target_url = 'https://www.novipnoad.net/'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }
    
    print("=" * 60)
    print("开始分析网站结构...")
    print(f"目标网站: {target_url}")
    print("=" * 60)
    
    try:
        # 访问主页
        print("\n1. 访问主页...")
        response = requests.get(target_url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"页面大小: {len(response.text)} 字符")

        if response.status_code != 200:
            print(f"访问失败，状态码: {response.status_code}")
            return

        # 检查内容编码并尝试禁用压缩
        content_encoding = response.headers.get('content-encoding', '')
        print(f"内容编码: {content_encoding}")

        # 如果有压缩，尝试禁用压缩重新获取
        if content_encoding in ['br', 'gzip', 'deflate']:
            print(f"检测到{content_encoding}压缩，尝试禁用压缩重新获取...")
            headers_no_encoding = headers.copy()
            headers_no_encoding['Accept-Encoding'] = 'identity'
            response2 = requests.get(target_url, headers=headers_no_encoding, timeout=10)
            html_content = response2.text
            print(f"重新获取后的内容前100字符: {html_content[:100]}")
        else:
            html_content = response.text
            print(f"HTML内容前100字符: {html_content[:100]}")

        # 解析HTML
        data = pq(html_content)
        print(f"页面标题: {data('title').text()}")
        
        # 分析页面结构
        print("\n2. 分析页面结构...")
        
        # 查找可能的视频列表容器
        selectors_to_check = [
            '.module-item', '.video-item', '.movie-item', '.card', '.list-item',
            '.item', '.video', '.movie', '.content-item', '.post-item',
            '.video-list li', '.movie-list li', '.content-list li',
            'article', '.article', '.entry', '.post'
        ]
        
        found_containers = []
        for selector in selectors_to_check:
            elements = data(selector)
            if len(elements) > 0:
                found_containers.append({
                    'selector': selector,
                    'count': len(elements),
                    'sample_html': str(elements.eq(0))[:200] + '...' if len(str(elements.eq(0))) > 200 else str(elements.eq(0))
                })
        
        print("找到的可能视频容器:")
        for container in found_containers:
            print(f"  选择器: {container['selector']}")
            print(f"  数量: {container['count']}")
            print(f"  示例HTML: {container['sample_html']}")
            print()
        
        # 分析链接模式
        print("\n3. 分析链接模式...")
        all_links = data('a[href]')
        detail_patterns = [
            r'/detail/', r'/video/', r'/movie/', r'/play/', r'/watch/',
            r'/v/', r'/m/', r'/show/', r'/film/', r'/drama/'
        ]
        
        detail_links = []
        for link in all_links.items():
            href = link.attr('href')
            if href:
                for pattern in detail_patterns:
                    if re.search(pattern, href):
                        detail_links.append(href)
                        break
        
        print(f"找到 {len(detail_links)} 个可能的详情链接")
        if detail_links:
            print("详情链接示例:")
            for i, link in enumerate(detail_links[:5]):
                print(f"  {i+1}. {link}")
        
        # 分析导航菜单
        print("\n4. 分析导航菜单...")
        nav_selectors = ['nav', '.nav', '.menu', '.navigation', '.header-menu', '.main-menu']
        for selector in nav_selectors:
            nav_elements = data(selector)
            if len(nav_elements) > 0:
                print(f"导航容器: {selector}")
                nav_links = nav_elements('a')
                print(f"导航链接数量: {len(nav_links)}")
                for i, nav_link in enumerate(nav_links.items()):
                    if i < 10:  # 只显示前10个
                        text = nav_link.text().strip()
                        href = nav_link.attr('href')
                        if text and href:
                            print(f"  {text}: {href}")
                break
        
        # 分析搜索功能
        print("\n5. 分析搜索功能...")
        search_forms = data('form')
        search_inputs = data('input[type="search"], input[name*="search"], input[name*="keyword"], input[name*="wd"]')
        
        print(f"找到 {len(search_forms)} 个表单")
        print(f"找到 {len(search_inputs)} 个搜索输入框")
        
        for i, form in enumerate(search_forms.items()):
            action = form.attr('action')
            method = form.attr('method') or 'GET'
            print(f"  表单 {i+1}: action={action}, method={method}")
        
        for i, input_elem in enumerate(search_inputs.items()):
            name = input_elem.attr('name')
            placeholder = input_elem.attr('placeholder')
            print(f"  搜索框 {i+1}: name={name}, placeholder={placeholder}")
        
        # 分析分页
        print("\n6. 分析分页...")
        pagination_selectors = ['.pagination', '.page', '.pager', '.page-nav', '.page-list']
        for selector in pagination_selectors:
            pagination = data(selector)
            if len(pagination) > 0:
                print(f"分页容器: {selector}")
                page_links = pagination('a')
                print(f"分页链接数量: {len(page_links)}")
                for i, page_link in enumerate(page_links.items()):
                    if i < 5:  # 只显示前5个
                        text = page_link.text().strip()
                        href = page_link.attr('href')
                        print(f"  {text}: {href}")
                break
        
        # 保存页面源码用于进一步分析
        with open('novipnoad_homepage.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"\n页面源码已保存到: novipnoad_homepage.html")

        # 额外分析视频项结构
        print("\n7. 详细分析视频项结构...")
        video_items = data('.video-item')
        if len(video_items) > 0:
            first_item = video_items.eq(0)
            print("第一个视频项的详细结构:")
            print(f"  完整HTML: {str(first_item)[:500]}...")

            # 分析链接
            link = first_item('a').attr('href')
            print(f"  详情链接: {link}")

            # 分析标题
            title = first_item('a').attr('title') or first_item('.item-title').text()
            print(f"  标题: {title}")

            # 分析图片
            img = first_item('img').attr('src') or first_item('img').attr('data-src')
            print(f"  图片: {img}")

            # 分析其他信息
            meta = first_item('.item-meta').text() if first_item('.item-meta') else ''
            print(f"  元信息: {meta}")

        # 分析搜索表单
        print("\n8. 详细分析搜索表单...")
        search_form = data('form').eq(0)
        if len(search_form) > 0:
            print(f"  表单HTML: {str(search_form)[:300]}...")
            inputs = search_form('input')
            for i, inp in enumerate(inputs.items()):
                name = inp.attr('name')
                type_attr = inp.attr('type')
                placeholder = inp.attr('placeholder')
                print(f"  输入框 {i+1}: name={name}, type={type_attr}, placeholder={placeholder}")
        
        print("\n" + "=" * 60)
        print("网站结构分析完成")
        print("=" * 60)
        
    except requests.exceptions.RequestException as e:
        print(f"网络请求失败: {e}")
    except Exception as e:
        print(f"分析过程中出现错误: {e}")

if __name__ == "__main__":
    analyze_website()
