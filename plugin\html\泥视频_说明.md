# 泥视频插件说明

## 插件信息
- **插件名称**: 泥视频
- **网站地址**: https://www.nivod.vip/
- **网站描述**: 海外华人在线影院
- **插件类型**: HTML网页类型
- **开发日期**: 2025-08-04

## 功能特性

### ✅ 已实现功能
1. **首页内容获取** - 获取网站首页推荐视频和分类信息
2. **分类浏览** - 支持电影、剧集、综艺、动漫四大分类
3. **分页浏览** - 支持分类页面的分页功能
4. **搜索功能** - 支持关键词搜索视频内容
5. **详情页面** - 获取视频详细信息、剧集列表、播放源
6. **播放解析** - 解析播放页面，获取视频播放地址

### 📊 测试结果
- ✅ 首页获取: 70个视频，4个分类
- ✅ 分类浏览: 80个视频/页
- ✅ 搜索功能: 正常返回搜索结果
- ✅ 详情页面: 完整获取视频信息和9个播放源
- ✅ 播放解析: 正常解析播放地址

## 技术实现

### 使用的Python模块
- **requests**: HTTP请求库
- **pyquery**: jQuery风格HTML解析
- **re**: 正则表达式处理
- **json**: JSON数据处理
- **urllib.parse**: URL编码处理

### 网站结构分析
- **URL模式**:
  - 主页: `https://www.nivod.vip/`
  - 分类: `https://www.nivod.vip/t/{type_id}/`
  - 分页: `https://www.nivod.vip/k/{type_id}-----------/{page}/`
  - 详情: `https://www.nivod.vip/nivod/{video_id}/`
  - 播放: `https://www.nivod.vip/niplay/{video_id}-{sid}-{nid}/`
  - 搜索: `https://www.nivod.vip/s/-------------/?wd={keyword}`

- **HTML结构**:
  - 首页/分类页: `.module-item` 选择器
  - 搜索页: `.module-card-item` 选择器
  - 详情页: `.module-info-*` 系列选择器
  - 播放页: 正则表达式提取播放地址

### 分类映射
- 1: 电影
- 2: 剧集
- 3: 综艺
- 4: 动漫

## 数据格式

### 视频列表项
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题",
    "vod_pic": "封面图片URL",
    "vod_year": "年份",
    "vod_remarks": "备注信息（如集数）"
}
```

### 视频详情
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题",
    "vod_pic": "封面图片URL",
    "type_name": "类型",
    "vod_year": "年份",
    "vod_area": "地区",
    "vod_lang": "语言",
    "vod_director": "导演",
    "vod_actor": "主演",
    "vod_content": "剧情简介",
    "vod_play_from": "播放源列表（$$$分隔）",
    "vod_play_url": "播放地址列表（$$$分隔）"
}
```

### 播放解析
```json
{
    "parse": 1,
    "url": "播放页面URL",
    "header": {
        "User-Agent": "浏览器标识"
    }
}
```

## 使用说明

### 安装要求
- Python 3.7+
- 已安装依赖包: requests, pyquery

### 使用方法
1. 将插件文件放置在 `plugin/html/` 目录下
2. 在PyramidStore中加载插件
3. 即可使用所有功能

### 测试方法
运行测试脚本验证功能:
```bash
python test_泥视频.py
```

## 注意事项

1. **网络要求**: 需要能够访问 https://www.nivod.vip/
2. **请求频率**: 建议控制请求频率，避免被网站限制
3. **User-Agent**: 插件已设置合适的浏览器标识
4. **编码处理**: 自动处理中文编码问题
5. **错误处理**: 包含完善的异常处理机制

## 更新日志

### v1.0.0 (2025-08-04)
- ✅ 初始版本发布
- ✅ 实现所有基础功能
- ✅ 通过完整功能测试
- ✅ 支持多播放源解析

## 开发者信息
- 基于PyramidStore框架开发
- 参考模板: plugin/html/嗷呜动漫.py
- 严格遵循项目编码规范
- 使用项目指定的依赖模块
