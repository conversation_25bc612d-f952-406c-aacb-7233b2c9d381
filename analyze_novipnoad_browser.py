# -*- coding: utf-8 -*-
# 使用浏览器自动化分析NO视频播放机制
import requests
import re
import json
import time
from urllib.parse import urlencode, quote

def analyze_with_browser_simulation():
    """模拟浏览器行为分析播放机制"""
    
    print("=" * 80)
    print("模拟浏览器分析NO视频播放机制")
    print("=" * 80)
    
    # 基础信息
    base_url = 'https://www.novipnoad.net'
    detail_url = 'https://www.novipnoad.net/tv/western/150717.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',
        'Referer': detail_url,
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    }
    
    try:
        # 1. 获取详情页面
        print(f"\n1. 获取详情页面...")
        response = requests.get(detail_url, headers=headers, timeout=15)
        content = response.text
        
        # 提取关键信息
        pkey_match = re.search(r'window\.playInfo\s*=\s*\{\s*pkey\s*:\s*["\']([^"\']+)["\']', content)
        pkey = pkey_match.group(1) if pkey_match else None
        print(f"播放密钥: {pkey}")
        
        vid_matches = re.findall(r'data-vid=["\']([^"\']+)["\']', content)
        print(f"正则匹配的视频ID列表: {vid_matches}")

        # 如果正则没找到，使用pyquery查找
        if not vid_matches:
            from pyquery import PyQuery as pq
            data = pq(content)

            # 查找所有可能的播放按钮
            selectors = [
                '.multilink-btn[data-vid]',
                '[data-vid]',
                '.play-btn[data-vid]',
                '.episode-btn[data-vid]'
            ]

            for selector in selectors:
                elements = data(selector)
                if len(elements) > 0:
                    print(f"找到播放按钮: {selector} ({len(elements)}个)")
                    for i, elem in enumerate(elements.items()):
                        vid = elem.attr('data-vid')
                        text = elem.text().strip()
                        if vid:
                            vid_matches.append(vid)
                            print(f"  按钮{i+1}: {text} -> {vid}")
                    break

        if not pkey:
            print("❌ 缺少播放密钥")
            return

        if not vid_matches:
            print("❌ 缺少视频ID，尝试其他方法...")
            # 保存页面用于调试
            with open('debug_page.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("页面已保存到 debug_page.html")

            # 尝试从URL中提取可能的ID
            url_id_match = re.search(r'/(\d+)\.html', detail_url)
            if url_id_match:
                test_vid = url_id_match.group(1)
                vid_matches = [test_vid]
                print(f"从URL提取的ID: {test_vid}")
            else:
                return
        
        # 2. 尝试常见的播放API模式
        print(f"\n2. 尝试播放API...")
        
        test_vid = vid_matches[0]
        print(f"测试视频ID: {test_vid}")
        
        # 常见的播放API端点和参数组合
        api_tests = [
            # 端点1: /lib/ajax.php
            {
                'url': f'{base_url}/lib/ajax.php',
                'params': [
                    {'action': 'get_play_url', 'vid': test_vid, 'pkey': pkey},
                    {'action': 'play', 'vid': test_vid, 'key': pkey},
                    {'action': 'video', 'id': test_vid, 'token': pkey},
                    {'vid': test_vid, 'pkey': pkey},
                    {'v': test_vid, 'k': pkey},
                ]
            },
            # 端点2: /api/play
            {
                'url': f'{base_url}/api/play',
                'params': [
                    {'vid': test_vid, 'pkey': pkey},
                    {'id': test_vid, 'key': pkey},
                ]
            },
            # 端点3: /play/get
            {
                'url': f'{base_url}/play/get',
                'params': [
                    {'vid': test_vid, 'pkey': pkey},
                ]
            }
        ]
        
        for api_test in api_tests:
            api_url = api_test['url']
            print(f"\n测试API: {api_url}")
            
            for params in api_test['params']:
                print(f"  参数: {params}")
                
                # GET请求
                try:
                    get_url = f"{api_url}?{urlencode(params)}"
                    get_response = requests.get(get_url, headers=headers, timeout=10)
                    if get_response.status_code == 200 and get_response.text.strip():
                        print(f"    GET响应: {get_response.text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(get_response.text)
                            print(f"    JSON数据: {json_data}")
                            
                            # 查找播放地址
                            for key in ['url', 'play_url', 'video_url', 'src', 'link', 'data']:
                                if key in json_data:
                                    value = json_data[key]
                                    if isinstance(value, str) and ('http' in value or '.m3u8' in value or '.mp4' in value):
                                        print(f"    🎯 找到播放地址: {value}")
                                    elif isinstance(value, dict):
                                        print(f"    📋 播放数据: {value}")
                        except:
                            # 可能是纯文本响应
                            if 'http' in get_response.text and ('.m3u8' in get_response.text or '.mp4' in get_response.text):
                                print(f"    🎯 文本中的播放地址: {get_response.text}")
                except Exception as e:
                    pass
                
                # POST请求
                try:
                    post_response = requests.post(api_url, data=params, headers=headers, timeout=10)
                    if post_response.status_code == 200 and post_response.text.strip():
                        print(f"    POST响应: {post_response.text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(post_response.text)
                            print(f"    JSON数据: {json_data}")
                            
                            # 查找播放地址
                            for key in ['url', 'play_url', 'video_url', 'src', 'link', 'data']:
                                if key in json_data:
                                    value = json_data[key]
                                    if isinstance(value, str) and ('http' in value or '.m3u8' in value or '.mp4' in value):
                                        print(f"    🎯 找到播放地址: {value}")
                                    elif isinstance(value, dict):
                                        print(f"    📋 播放数据: {value}")
                        except:
                            # 可能是纯文本响应
                            if 'http' in post_response.text and ('.m3u8' in post_response.text or '.mp4' in post_response.text):
                                print(f"    🎯 文本中的播放地址: {post_response.text}")
                except Exception as e:
                    pass
        
        # 3. 分析可能的加密参数
        print(f"\n3. 分析加密参数...")
        
        # 尝试解码pkey
        import base64
        import urllib.parse
        
        try:
            # URL解码
            decoded_pkey = urllib.parse.unquote(pkey)
            print(f"URL解码后的pkey: {decoded_pkey}")
            
            # Base64解码
            try:
                base64_decoded = base64.b64decode(decoded_pkey + '==')  # 添加padding
                print(f"Base64解码结果: {base64_decoded}")
            except:
                print("Base64解码失败")
                
        except Exception as e:
            print(f"解码失败: {e}")
        
        # 4. 尝试构建播放请求
        print(f"\n4. 尝试构建播放请求...")
        
        # 模拟点击播放按钮的请求
        play_headers = headers.copy()
        play_headers.update({
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        })
        
        # 尝试不同的请求格式
        play_requests = [
            {
                'url': f'{base_url}/lib/ajax.php',
                'data': {
                    'action': 'get_play_url',
                    'vid': test_vid,
                    'pkey': pkey,
                    'timestamp': str(int(time.time())),
                }
            },
            {
                'url': f'{base_url}/play',
                'data': {
                    'vid': test_vid,
                    'key': pkey,
                }
            }
        ]
        
        for req in play_requests:
            try:
                print(f"  尝试: {req['url']}")
                print(f"  数据: {req['data']}")
                
                response = requests.post(req['url'], data=req['data'], headers=play_headers, timeout=10)
                print(f"  状态码: {response.status_code}")
                print(f"  响应: {response.text[:300]}...")
                
                if response.status_code == 200 and response.text.strip():
                    try:
                        json_data = json.loads(response.text)
                        print(f"  JSON解析: {json_data}")
                        
                        # 查找播放地址
                        for key in ['url', 'play_url', 'video_url', 'src', 'link']:
                            if key in json_data:
                                print(f"  🎯 播放地址({key}): {json_data[key]}")
                    except:
                        pass
                        
            except Exception as e:
                print(f"  请求失败: {e}")
        
        print("\n" + "=" * 80)
        print("浏览器模拟分析完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_with_browser_simulation()
