# -*- coding: utf-8 -*-
# 哔咪动漫插件测试脚本
import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_spider():
    """测试哔咪动漫插件的所有功能"""
    try:
        # 导入插件
        from plugin.html.哔咪动漫 import Spider

        print("=" * 60)
        print("开始测试哔咪动漫插件")
        print("=" * 60)

        # 创建爬虫实例
        spider = Spider()
        spider.init()

        print(f"插件名称: {spider.getName()}")
        print(f"网站地址: {spider.host}")

        # 1. 测试首页内容
        print("\n1. 测试首页内容...")
        try:
            home_result = spider.homeContent({})
            print(f"分类数量: {len(home_result.get('class', []))}")
            print("分类列表:")
            for cat in home_result.get('class', []):
                print(f"  - {cat['type_name']} (ID: {cat['type_id']})")

            print(f"首页视频数量: {len(home_result.get('list', []))}")
            if home_result.get('list'):
                print("首页视频示例:")
                for i, video in enumerate(home_result['list'][:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
                    print(f"     图片: {video['vod_pic']}")
                    print(f"     备注: {video['vod_remarks']}")
        except Exception as e:
            print(f"首页测试失败: {e}")

        # 2. 测试分类内容
        print("\n2. 测试分类内容...")
        try:
            category_result = spider.categoryContent('riman', '1', {}, {})
            print(f"分类视频数量: {len(category_result.get('list', []))}")
            print(f"当前页: {category_result.get('page', 0)}")
            print(f"总页数: {category_result.get('pagecount', 0)}")

            if category_result.get('list'):
                print("分类视频示例:")
                for i, video in enumerate(category_result['list'][:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
                    print(f"     图片: {video['vod_pic']}")
                    print(f"     备注: {video['vod_remarks']}")
        except Exception as e:
            print(f"分类测试失败: {e}")

        # 3. 测试搜索功能
        print("\n3. 测试搜索功能...")
        try:
            search_result = spider.searchContent('火影', False, '1')
            print(f"搜索结果数量: {len(search_result.get('list', []))}")

            if search_result.get('list'):
                print("搜索结果示例:")
                for i, video in enumerate(search_result['list'][:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
                    print(f"     图片: {video['vod_pic']}")
                    print(f"     年份: {video['vod_year']}")
                    print(f"     备注: {video['vod_remarks']}")
        except Exception as e:
            print(f"搜索测试失败: {e}")

        # 4. 测试详情和播放
        print("\n4. 测试详情和播放...")
        try:
            # 使用分类结果中的第一个视频进行测试
            if 'category_result' in locals() and category_result.get('list'):
                test_id = category_result['list'][0]['vod_id']
                print(f"测试视频ID: {test_id}")

                # 测试详情
                detail_result = spider.detailContent([test_id])
                if detail_result.get('list'):
                    detail = detail_result['list'][0]
                    print(f"详情标题: {detail['vod_name']}")
                    print(f"详情图片: {detail['vod_pic']}")
                    print(f"类型: {detail['type_name']}")
                    print(f"年份: {detail['vod_year']}")
                    print(f"地区: {detail['vod_area']}")
                    print(f"导演: {detail['vod_director']}")
                    print(f"演员: {detail['vod_actor']}")
                    print(f"播放源: {detail['vod_play_from']}")

                    # 测试播放
                    if detail['vod_play_url']:
                        play_urls = detail['vod_play_url'].split('$$$')
                        if play_urls and play_urls[0]:
                            first_episode = play_urls[0].split('#')[0]
                            if '$' in first_episode:
                                ep_name, ep_url = first_episode.split('$', 1)
                                print(f"测试播放: {ep_name} -> {ep_url}")

                                play_result = spider.playerContent('', ep_url, [])
                                print(f"播放解析模式: {play_result.get('parse', 'unknown')}")
                                print(f"播放地址: {play_result.get('url', 'none')}")
                else:
                    print("详情获取失败")
            else:
                print("没有可用的测试视频ID")
        except Exception as e:
            print(f"详情和播放测试失败: {e}")

        print("\n" + "=" * 60)
        print("哔咪动漫插件测试完成")
        print("=" * 60)

    except ImportError as e:
        print(f"导入插件失败: {e}")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spider()
