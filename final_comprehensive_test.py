# -*- coding: utf-8 -*-
# NO视频插件最终综合测试
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'plugin', 'html'))

from NO视频 import Spider

def comprehensive_test():
    """NO视频插件最终综合测试"""
    
    print("=" * 80)
    print("NO视频插件最终综合测试")
    print("=" * 80)
    
    spider = Spider()
    
    # 测试用例
    test_cases = [
        {
            'name': '首页功能测试',
            'method': 'homeContent',
            'args': [True]
        },
        {
            'name': '分类功能测试', 
            'method': 'categoryContent',
            'args': ['1', 1, True, {}]
        },
        {
            'name': '搜索功能测试',
            'method': 'searchContent',
            'args': ['荒境', True]
        },
        {
            'name': '详情功能测试',
            'method': 'detailContent',
            'args': [['150717']]
        },
        {
            'name': '播放功能测试',
            'method': 'playerContent',
            'args': ['', 'https://www.novipnoad.net/tv/western/150717.html?vid=ftn-1752837192', []]
        }
    ]
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n{'='*60}")
        print(f"🧪 {test_case['name']}")
        print(f"{'='*60}")
        
        try:
            method = getattr(spider, test_case['method'])
            result = method(*test_case['args'])
            
            if test_case['method'] == 'homeContent':
                print(f"✅ 首页分类数: {len(result.get('class', []))}")
                print(f"✅ 首页视频数: {len(result.get('list', []))}")
                
                # 测试标题清理
                if result.get('list'):
                    sample_video = result['list'][0]
                    original_title = sample_video.get('vod_name', '')
                    print(f"✅ 标题示例: {original_title}")
                    
                    # 检查是否包含多余信息
                    has_brackets = '【' in original_title or '(' in original_title
                    has_suffix = '_' in original_title or '–' in original_title
                    
                    if not has_brackets and not has_suffix:
                        print("✅ 标题清理: 完美工作")
                    else:
                        print("⚠️ 标题清理: 可能需要进一步优化")
                
            elif test_case['method'] == 'categoryContent':
                print(f"✅ 分类视频数: {len(result.get('list', []))}")
                print(f"✅ 分页信息: 第{result.get('page', 0)}页")
                
            elif test_case['method'] == 'searchContent':
                print(f"✅ 搜索结果数: {len(result.get('list', []))}")
                if result.get('list'):
                    sample_result = result['list'][0]
                    print(f"✅ 搜索示例: {sample_result.get('vod_name', '')}")
                
            elif test_case['method'] == 'detailContent':
                if result.get('list'):
                    detail = result['list'][0]
                    print(f"✅ 视频标题: {detail.get('vod_name', '')}")
                    print(f"✅ 播放源数: {len(detail.get('vod_play_from', '').split('$$$'))}")
                    
                    # 分析播放列表
                    play_urls = detail.get('vod_play_url', '')
                    if play_urls:
                        episodes = play_urls.split('#')
                        print(f"✅ 剧集数量: {len(episodes)}")
                        
                        # 检查是否有vid参数
                        vid_count = sum(1 for ep in episodes if '?vid=' in ep)
                        print(f"✅ 带vid参数的剧集: {vid_count}/{len(episodes)}")
                        
                        # 显示前3集示例
                        print("✅ 剧集示例:")
                        for i, episode in enumerate(episodes[:3]):
                            if '$' in episode:
                                ep_name, ep_url = episode.split('$', 1)
                                print(f"  {i+1}. {ep_name} -> {ep_url[:50]}...")
                
            elif test_case['method'] == 'playerContent':
                print(f"✅ 解析模式: parse={result.get('parse', 'unknown')}")
                print(f"✅ 播放地址: {result.get('url', '')[:100]}...")
                
                # 分析播放地址类型
                play_url = result.get('url', '')
                if '.m3u8' in play_url:
                    print("✅ 地址类型: HLS流媒体 (.m3u8)")
                elif '.mp4' in play_url:
                    print("✅ 地址类型: MP4视频文件")
                elif '?vid=' in play_url:
                    print("✅ 地址类型: 带vid参数的专用播放页面")
                elif 'iframe' in play_url or 'player' in play_url:
                    print("✅ 地址类型: 嵌入式播放器")
                else:
                    print("✅ 地址类型: 标准网页")
                
                # 检查请求头
                headers = result.get('header', {})
                print(f"✅ 请求头数量: {len(headers)}")
            
            results[test_case['name']] = {
                'status': 'success',
                'result': result
            }
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results[test_case['name']] = {
                'status': 'failed',
                'error': str(e)
            }
    
    # 生成测试报告
    print(f"\n{'='*80}")
    print("📊 最终测试报告")
    print(f"{'='*80}")
    
    success_count = sum(1 for r in results.values() if r['status'] == 'success')
    total_count = len(results)
    
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    print(f"\n详细结果:")
    for test_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"{status_icon} {test_name}: {result['status']}")
        if result['status'] == 'failed':
            print(f"   错误: {result['error']}")
    
    # 功能评估
    print(f"\n{'='*60}")
    print("🎯 功能评估")
    print(f"{'='*60}")
    
    if 'detailContent' in [name for name, result in results.items() if result['status'] == 'success']:
        detail_result = results['详情功能测试']['result']
        if detail_result.get('list'):
            detail = detail_result['list'][0]
            title = detail.get('vod_name', '')
            
            # 评估标题清理效果
            title_clean = not any(marker in title for marker in ['【', '】', '(', ')', '_', '–', 'NO视频'])
            print(f"✅ 标题清理功能: {'完美' if title_clean else '需要优化'}")
            
            # 评估播放列表
            play_urls = detail.get('vod_play_url', '')
            episodes = play_urls.split('#') if play_urls else []
            vid_episodes = [ep for ep in episodes if '?vid=' in ep]
            
            print(f"✅ 播放列表解析: {len(episodes)}集")
            print(f"✅ vid参数支持: {len(vid_episodes)}/{len(episodes)}集")
    
    if 'playerContent' in [name for name, result in results.items() if result['status'] == 'success']:
        player_result = results['播放功能测试']['result']
        parse_mode = player_result.get('parse', 0)
        play_url = player_result.get('url', '')
        
        print(f"✅ 播放地址解析: {'直接播放' if parse_mode == 0 else '需要进一步解析'}")
        print(f"✅ 地址有效性: {'有效' if play_url else '无效'}")
    
    print(f"\n{'='*60}")
    print("🏆 最终结论")
    print(f"{'='*60}")
    
    if success_count >= 4:
        print("🎉 NO视频插件优化成功！")
        print("✅ 所有核心功能正常工作")
        print("✅ 标题显示已优化")
        print("✅ 播放功能已改进")
        print("✅ 插件稳定可用")
    elif success_count >= 3:
        print("✅ NO视频插件基本可用")
        print("⚠️ 部分功能可能需要进一步优化")
    else:
        print("❌ 插件存在问题，需要进一步调试")
    
    print(f"\n{'='*80}")
    print("测试完成")
    print(f"{'='*80}")
    
    return results

if __name__ == "__main__":
    comprehensive_test()
