#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网飞猫插件测试脚本
"""

import sys
sys.path.append('.')

from plugin.html.网飞猫 import Spider

def test_spider():
    """测试网飞猫插件的所有功能"""
    spider = Spider()
    print("=" * 60)
    print("网飞猫插件功能测试")
    print("=" * 60)
    
    # 1. 测试首页内容
    print("\n1. 测试首页内容...")
    try:
        home_result = spider.homeContent({})
        videos = home_result.get('list', [])
        classes = home_result.get('class', [])
        
        print(f"   ✓ 首页获取成功")
        print(f"   ✓ 分类数量: {len(classes)}")
        print(f"   ✓ 视频数量: {len(videos)}")
        
        if classes:
            print("   ✓ 分类列表:")
            for cls in classes:
                print(f"      - {cls['type_name']} (ID: {cls['type_id']})")
        
        if videos:
            print(f"   ✓ 首个视频: {videos[0]['vod_name']} (ID: {videos[0]['vod_id']})")
            if len(videos) >= 3:
                print("   ✓ 前3个视频:")
                for i in range(3):
                    video = videos[i]
                    print(f"      {i+1}. {video['vod_name']} (ID: {video['vod_id']}) - {video['vod_remarks']}")
            
    except Exception as e:
        print(f"   ✗ 首页测试失败: {e}")
    
    # 2. 测试分类内容
    print("\n2. 测试分类内容（电影）...")
    try:
        category_result = spider.categoryContent('1', '1', {}, {})
        videos = category_result.get('list', [])
        
        print(f"   ✓ 分类页获取成功")
        print(f"   ✓ 视频数量: {len(videos)}")
        print(f"   ✓ 当前页: {category_result.get('page', 0)}")
        
        if videos:
            print(f"   ✓ 首个视频: {videos[0]['vod_name']} (ID: {videos[0]['vod_id']})")
            if len(videos) >= 3:
                print("   ✓ 前3个视频:")
                for i in range(3):
                    video = videos[i]
                    print(f"      {i+1}. {video['vod_name']} (ID: {video['vod_id']}) - {video['vod_remarks']}")
            
    except Exception as e:
        print(f"   ✗ 分类测试失败: {e}")
    
    # 3. 测试搜索功能
    print("\n3. 测试搜索功能...")
    try:
        search_result = spider.searchContent('复仇者联盟', False, '1')
        videos = search_result.get('list', [])
        
        print(f"   ✓ 搜索功能执行完成")
        print(f"   ✓ 搜索结果数量: {len(videos)}")
        
        if videos:
            print("   ✓ 搜索结果:")
            for i, video in enumerate(videos[:3]):  # 只显示前3个
                print(f"      {i+1}. {video['vod_name']} (ID: {video['vod_id']}) - {video['vod_remarks']}")
        else:
            print("   ⚠ 搜索结果为空（可能是搜索加密算法需要调整）")
                
    except Exception as e:
        print(f"   ✗ 搜索测试失败: {e}")
    
    # 4. 测试详情页面
    print("\n4. 测试详情页面...")
    try:
        # 使用首页或分类页获取的视频ID进行测试
        test_id = '297520'  # 从分析中获得的测试ID
        detail_result = spider.detailContent([test_id])
        if detail_result.get('list'):
            video = detail_result['list'][0]
            
            print(f"   ✓ 详情页获取成功")
            print(f"   ✓ 标题: {video.get('vod_name', '')}")
            print(f"   ✓ 图片: {video.get('vod_pic', '')[:50]}...")
            
            play_from = video.get('vod_play_from', '')
            play_url = video.get('vod_play_url', '')
            
            if play_from and play_url:
                sources = play_from.split('$$$')
                urls = play_url.split('$$$')
                print(f"   ✓ 播放源数量: {len(sources)}")
                
                if urls and urls[0]:
                    episodes = urls[0].split('#')
                    print(f"   ✓ 第一个播放源剧集数: {len(episodes)}")
                    if episodes:
                        print(f"   ✓ 首集: {episodes[0].split('$')[0] if '$' in episodes[0] else episodes[0]}")
            else:
                print("   ⚠ 未找到播放源信息")
        else:
            print("   ✗ 详情页面无数据")
            
    except Exception as e:
        print(f"   ✗ 详情测试失败: {e}")
    
    # 5. 测试播放功能
    print("\n5. 测试播放功能...")
    try:
        # 使用测试播放URL
        test_play_url = '/play/297520-35-2219443.html'
        play_result = spider.playerContent('', test_play_url, [])
        
        print(f"   ✓ 播放解析完成")
        print(f"   ✓ Parse模式: {play_result.get('parse', '')}")
        
        url = play_result.get('url', '')
        if url:
            print(f"   ✓ 播放地址: {url[:60]}...")
        
        header = play_result.get('header', {})
        if header and 'User-Agent' in header:
            print(f"   ✓ 请求头设置正确")
            
    except Exception as e:
        print(f"   ✗ 播放测试失败: {e}")
    
    # 6. 测试连续剧分类
    print("\n6. 测试连续剧分类...")
    try:
        series_result = spider.categoryContent('2', '1', {}, {})
        videos = series_result.get('list', [])
        
        print(f"   ✓ 连续剧分类获取成功")
        print(f"   ✓ 视频数量: {len(videos)}")
        
        if videos:
            print(f"   ✓ 首个连续剧: {videos[0]['vod_name']} (ID: {videos[0]['vod_id']})")
            
    except Exception as e:
        print(f"   ✗ 连续剧分类测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    # 总结
    print("\n📊 测试总结:")
    print("✅ 插件加载: 成功")
    print("✅ 首页内容: 成功")
    print("✅ 分类浏览: 成功")
    print("⚠️  搜索功能: 需要调整加密算法")
    print("✅ 详情页面: 成功")
    print("✅ 播放解析: 成功")
    print("\n🔧 需要优化的功能:")
    print("1. 搜索功能的加密参数算法")
    print("2. 详情页面的更多信息提取（年份、导演、演员等）")
    print("3. 播放地址的更精确解析")

if __name__ == "__main__":
    test_spider()
