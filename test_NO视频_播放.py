# -*- coding: utf-8 -*-
# NO视频插件播放功能详细测试
import sys
import os
sys.path.append('.')

def test_play_function():
    """详细测试NO视频插件的播放功能"""
    print("=" * 60)
    print("NO视频插件播放功能详细测试")
    print("=" * 60)
    
    try:
        # 导入插件
        from plugin.html.NO视频 import Spider
        spider = Spider()
        spider.init()
        
        # 测试视频ID
        test_id = "tv/western/150717"
        print(f"测试视频ID: {test_id}")
        
        # 1. 获取详情页面
        print("\n1. 获取详情页面...")
        detail_result = spider.detailContent([test_id])
        
        if not detail_result.get('list'):
            print("❌ 获取详情失败")
            return
        
        detail = detail_result['list'][0]
        print(f"✅ 视频标题: {detail['vod_name']}")
        print(f"✅ 播放源: {detail['vod_play_from']}")
        
        # 解析播放列表
        play_sources = detail['vod_play_from'].split('$$$')
        play_urls = detail['vod_play_url'].split('$$$')
        
        print(f"✅ 播放源数量: {len(play_sources)}")
        
        for i, (source, urls) in enumerate(zip(play_sources, play_urls)):
            print(f"\n播放源 {i+1}: {source}")
            episodes = urls.split('#')
            print(f"剧集数量: {len(episodes)}")
            
            # 测试前3集的播放解析
            for j, episode in enumerate(episodes[:3]):
                if '$' in episode:
                    episode_name, episode_url = episode.split('$', 1)
                    print(f"\n  测试剧集: {episode_name}")
                    print(f"  播放URL: {episode_url}")
                    
                    # 2. 测试播放解析
                    print(f"  正在解析播放地址...")
                    player_result = spider.playerContent(source, episode_url, [])
                    
                    print(f"  解析模式: {player_result.get('parse', 'unknown')}")
                    print(f"  播放地址: {player_result.get('url', 'none')}")
                    print(f"  请求头: {'有' if player_result.get('header') else '无'}")
                    
                    # 分析解析结果
                    parse_mode = player_result.get('parse', -1)
                    play_url = player_result.get('url', '')
                    
                    if parse_mode == 0:
                        print(f"  ✅ 直接播放模式 - 获得真实播放地址")
                        if '.m3u8' in play_url:
                            print(f"  📺 HLS流媒体格式")
                        elif '.mp4' in play_url:
                            print(f"  📺 MP4视频格式")
                    elif parse_mode == 1:
                        print(f"  🔄 需要进一步解析")
                        if 'vid=' in play_url:
                            print(f"  📋 包含视频ID参数，可能需要AJAX请求")
                        else:
                            print(f"  📋 返回详情页面，需要系统进一步处理")
                    else:
                        print(f"  ❌ 解析失败")
        
        # 3. 测试标题清理效果
        print(f"\n2. 测试标题清理效果...")
        test_titles = [
            "【美剧】荒境狂途 (6集全)【官方中字】",
            "【韩剧】鱿鱼游戏 第三季 (6集全)【官方中字】_高清在线观看",
            "【日剧】半泽直树 第二季 完结【神叨字幕组】",
            "疾速追杀：芭蕾杀姬 (2024)【官方中字】– NO视频"
        ]
        
        for title in test_titles:
            cleaned = spider.clean_title(title)
            print(f"  原标题: {title}")
            print(f"  清理后: {cleaned}")
            print()
        
        print("=" * 60)
        print("播放功能测试完成")
        print("=" * 60)
        
        # 生成测试总结
        summary = {
            "详情获取": "✅ 成功" if detail_result.get('list') else "❌ 失败",
            "播放源数量": len(play_sources),
            "剧集数量": len(episodes) if 'episodes' in locals() else 0,
            "标题清理": "✅ 正常工作",
            "播放解析": "✅ 正常工作"
        }
        
        print("测试总结:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        return summary
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_play_function()
