# -*- coding: utf-8 -*-
# 测试弹幕API解析播放地址
import requests
import re
import base64
from urllib.parse import quote, unquote

def test_danmu_api():
    """测试弹幕API解析播放地址"""

    # 从测试中获取的加密播放链接
    encrypted_link = "81bcAx7Alkd_tFYxzgHLvI0Rhvm8RMQcyq57ZtJO52Br8y-8lxHgHNWD9pcOUjQJ6POUnOO4t1eevp4agBYaY_xvdFacSe1fnYxV-3N0WdZFQnOm78os6vLzYhPi1EcPh-KH89KFgIiV"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    print("=" * 60)
    print("测试弹幕API解析播放地址")
    print("=" * 60)

    # 1. 尝试直接访问弹幕API
    print(f"\n1. 测试弹幕API:")
    danmu_urls = [
        f"http://www.bimiacg11.net/static/danmu/qy.php?url={encrypted_link}",
        f"http://www.bimiacg11.net/static/danmu/play.php?url={encrypted_link}",
        f"http://www.bimiacg11.net/danmu/qy.php?url={encrypted_link}",
        f"http://www.bimiacg11.net/danmu/play.php?url={encrypted_link}"
    ]

    for danmu_url in danmu_urls:
        try:
            print(f"  尝试访问: {danmu_url}")
            response = requests.get(danmu_url, headers=headers, timeout=10)
            print(f"  状态码: {response.status_code}")

            if response.status_code == 200:
                content = response.text
                print(f"  响应长度: {len(content)} 字符")
                print(f"  响应内容(前200字符): {content[:200]}")

                # 查找播放地址
                video_patterns = [
                    r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
                    r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
                    r'"url"[:\s]*"([^"]+)"',
                    r'"src"[:\s]*"([^"]+)"'
                ]

                for pattern in video_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"    找到播放地址: {matches}")

            else:
                print(f"  请求失败: {response.status_code}")

        except Exception as e:
            print(f"  请求异常: {e}")

    # 2. 尝试解密播放链接
    print(f"\n2. 尝试解密播放链接:")
    print(f"  原始链接: {encrypted_link}")

    # 尝试base64解码
    try:
        # 添加可能缺失的padding
        padded_link = encrypted_link + '=' * (4 - len(encrypted_link) % 4)
        decoded = base64.b64decode(padded_link).decode('utf-8')
        print(f"  Base64解码结果: {decoded}")
    except Exception as e:
        print(f"  Base64解码失败: {e}")

    # 尝试URL解码
    try:
        url_decoded = unquote(encrypted_link)
        print(f"  URL解码结果: {url_decoded}")
    except Exception as e:
        print(f"  URL解码失败: {e}")

    # 3. 分析播放页面的JavaScript
    print(f"\n3. 分析播放页面JavaScript:")
    play_url = "http://www.bimiacg11.net/bangumi/38328/play/1/1/"

    try:
        response = requests.get(play_url, headers=headers, timeout=10)
        if response.status_code == 200:
            content = response.text

            # 查找解密相关的JavaScript代码
            decrypt_patterns = [
                r'base64decode\([^)]+\)',
                r'unescape\([^)]+\)',
                r'decrypt\([^)]+\)',
                r'decode\([^)]+\)'
            ]

            for pattern in decrypt_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"  找到解密函数: {matches}")

            # 查找可能的解密密钥或算法
            key_patterns = [
                r'key["\s]*[:=]["\s]*([^"\']+)',
                r'secret["\s]*[:=]["\s]*([^"\']+)',
                r'salt["\s]*[:=]["\s]*([^"\']+)'
            ]

            for pattern in key_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"  找到可能的密钥: {matches}")

    except Exception as e:
        print(f"  分析JavaScript失败: {e}")

    print("\n" + "=" * 60)
    print("弹幕API测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_danmu_api()
