# -*- coding: utf-8 -*-
# 测试标准base64解密
import base64
from urllib.parse import unquote

def test_standard_base64():
    """测试标准base64解密"""
    print("=" * 80)
    print("测试标准base64解密")
    print("=" * 80)

    # 使用最新获取的加密URL
    test_url = "0E7vBXkLJVE22QfJMBebk2xPX5FsfJ0YQ93NcZpLiG6UIS7ePFMh3pAkuFBOc0tuPS4PkbjpTr7nDFwAqQkWPxyQuRlZN39vR1rGwsHyatVkyQGuhxqu/MGHLL7AVOrsC9l6fFvIRReN89X51bzT3JHBqFPvdk2hzGvC/7QQi2n3G4xUsgCU7zexAWHXBOcO"

    print(f"原始加密URL: {test_url}")

    # 方法1: 标准base64解码
    try:
        # 添加padding
        padded = test_url + '=' * (4 - len(test_url) % 4)
        decoded_bytes = base64.b64decode(padded)

        # 尝试不同编码
        for encoding in ['utf-8', 'latin-1', 'ascii', 'gbk']:
            try:
                decoded_str = decoded_bytes.decode(encoding)
                print(f"\n标准base64 + {encoding}: {decoded_str[:200]}...")

                # 尝试unescape
                unescaped = unquote(decoded_str)
                print(f"  Unescape结果: {unescaped[:200]}...")

                # 检查是否包含URL特征
                if any(indicator in unescaped.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv', '/']):
                    print(f"  ✅ 可能是有效URL!")

            except Exception as e:
                print(f"\n标准base64 + {encoding}: 解码失败 - {e}")

    except Exception as e:
        print(f"标准base64解码失败: {e}")

    # 方法2: URL安全base64解码
    try:
        url_safe = test_url.replace('-', '+').replace('_', '/')
        padded = url_safe + '=' * (4 - len(url_safe) % 4)
        decoded_bytes = base64.b64decode(padded)

        for encoding in ['utf-8', 'latin-1', 'ascii']:
            try:
                decoded_str = decoded_bytes.decode(encoding)
                print(f"\nURL安全base64 + {encoding}: {decoded_str[:200]}...")

                unescaped = unquote(decoded_str)
                print(f"  Unescape结果: {unescaped[:200]}...")

                if any(indicator in unescaped.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv', '/']):
                    print(f"  ✅ 可能是有效URL!")

            except Exception as e:
                print(f"\nURL安全base64 + {encoding}: 解码失败 - {e}")

    except Exception as e:
        print(f"URL安全base64解码失败: {e}")

    # 方法3: 尝试不同的字符替换
    replacements = [
        ('_', '/'),
        ('-', '+'),
        ('_', '/'),
        ('-', '+'),
    ]

    for i, (old, new) in enumerate(replacements):
        try:
            modified = test_url.replace(old, new)
            padded = modified + '=' * (4 - len(modified) % 4)
            decoded_bytes = base64.b64decode(padded)

            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            print(f"\n替换{old}->{new}: {decoded_str[:200]}...")

            unescaped = unquote(decoded_str)
            print(f"  Unescape结果: {unescaped[:200]}...")

            if any(indicator in unescaped.lower() for indicator in ['http', '.m3u8', '.mp4', '.flv', '/']):
                print(f"  ✅ 可能是有效URL!")

        except Exception as e:
            print(f"\n替换{old}->{new}: 解码失败 - {e}")

if __name__ == "__main__":
    test_standard_base64()
