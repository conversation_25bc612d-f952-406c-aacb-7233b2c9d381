# -*- coding: utf-8 -*-
# 泥视频插件 - 海外华人在线影院
import re
import sys
import json
import time
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        pass

    def getName(self):
        return "泥视频"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def action(self, action):
        pass

    def destroy(self):
        pass

    host = 'https://www.nivod.vip'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }

    def homeContent(self, filter):
        try:
            response = self.fetch(self.host, headers=self.headers)
            data = self.getpq(response.text)
            
            result = {}
            classes = []
            
            # 添加分类
            categories = [
                {'type_name': '电影', 'type_id': '1'},
                {'type_name': '剧集', 'type_id': '2'},
                {'type_name': '综艺', 'type_id': '3'},
                {'type_name': '动漫', 'type_id': '4'}
            ]
            classes.extend(categories)
            
            # 获取首页推荐内容
            videos = []
            items = data('.module-item')

            for item in items.items():
                try:
                    # 提取视频ID和链接
                    detail_link = item.attr('href')
                    if not detail_link:
                        continue

                    vod_id = re.search(r'/nivod/(\d+)/', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)

                    # 提取标题
                    title_elem = item('.module-poster-item-title')
                    vod_name = title_elem.text().strip()
                    if not vod_name:
                        vod_name = item.attr('title') or ''

                    # 提取图片
                    img_elem = item('.module-item-pic img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = self.host + vod_pic

                    # 提取备注（集数信息）
                    vod_remarks = item('.module-item-note').text().strip()

                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析首页视频项失败: {e}")
                    continue
            
            result['class'] = classes
            result['list'] = videos
            return result
            
        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return {'class': [], 'list': []}

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        try:
            url = f"{self.host}/t/{tid}/"
            if int(pg) > 1:
                url = f"{self.host}/k/{tid}-----------/{pg}/"
            
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            items = data('.module-item')

            for item in items.items():
                try:
                    # 提取视频ID和链接
                    detail_link = item.attr('href')
                    if not detail_link:
                        continue

                    vod_id = re.search(r'/nivod/(\d+)/', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)

                    # 提取标题
                    title_elem = item('.module-poster-item-title')
                    vod_name = title_elem.text().strip()
                    if not vod_name:
                        vod_name = item.attr('title') or ''

                    # 提取图片
                    img_elem = item('.module-item-pic img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = self.host + vod_pic

                    # 提取备注（集数信息）
                    vod_remarks = item('.module-item-note').text().strip()

                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析分类视频项失败: {e}")
                    continue
            
            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': 9999,  # 泥视频没有明确的总页数，设置较大值
                'limit': 20,
                'total': 999999
            }
            return result
            
        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 0, 'limit': 20, 'total': 0}

    def detailContent(self, ids):
        try:
            vod_id = ids[0]
            url = f"{self.host}/nivod/{vod_id}/"
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)
            
            # 提取基本信息
            title = data('.module-info-heading h1').text().strip()
            pic = data('.module-info-poster img').attr('data-original') or data('.module-info-poster img').attr('src')
            if pic and not pic.startswith('http'):
                pic = self.host + pic
            
            # 提取详细信息
            info_items = data('.module-info-item')
            vod_year = ''
            vod_area = ''
            vod_lang = ''
            vod_director = ''
            vod_actor = ''
            type_name = ''
            
            for item in info_items.items():
                item_text = item.text()
                if '年份' in item_text or '时间' in item_text:
                    vod_year = re.search(r'(\d{4})', item_text)
                    vod_year = vod_year.group(1) if vod_year else ''
                elif '地区' in item_text:
                    vod_area = item('a').text() or re.search(r'地区[：:]\s*([^/\n]+)', item_text)
                    if hasattr(vod_area, 'group'):
                        vod_area = vod_area.group(1).strip()
                elif '语言' in item_text:
                    vod_lang = item('a').text() or re.search(r'语言[：:]\s*([^/\n]+)', item_text)
                    if hasattr(vod_lang, 'group'):
                        vod_lang = vod_lang.group(1).strip()
                elif '导演' in item_text:
                    vod_director = item('a').text() or re.search(r'导演[：:]\s*([^/\n]+)', item_text)
                    if hasattr(vod_director, 'group'):
                        vod_director = vod_director.group(1).strip()
                elif '主演' in item_text or '演员' in item_text:
                    actors = [a.text() for a in item('a').items()]
                    vod_actor = ','.join(actors) if actors else ''
                elif '类型' in item_text:
                    types = [a.text() for a in item('a').items()]
                    type_name = ','.join(types) if types else ''
            
            # 提取剧情简介
            vod_content = data('.module-info-introduction-content').text().strip()
            
            # 提取播放列表
            play_sources = []
            play_urls = []
            
            # 查找播放源标签
            source_tabs = data('.module-tab-item')
            episode_lists = data('.module-play-list')
            
            for i, tab in enumerate(source_tabs.items()):
                source_name = tab.text().strip()
                if not source_name:
                    source_name = f"线路{i+1}"
                
                play_sources.append(source_name)
                
                # 获取对应的剧集列表
                if i < len(episode_lists):
                    episodes = []
                    episode_items = episode_lists.eq(i)('a')
                    
                    for ep in episode_items.items():
                        ep_name = ep.text().strip()
                        ep_url = ep.attr('href')
                        if ep_name and ep_url:
                            episodes.append(f"{ep_name}${ep_url}")
                    
                    play_urls.append('#'.join(episodes))
                else:
                    play_urls.append('')
            
            vod = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': vod_year,
                'vod_area': vod_area,
                'vod_lang': vod_lang,
                'vod_director': vod_director,
                'vod_actor': vod_actor,
                'vod_content': vod_content,
                'vod_play_from': '$$$'.join(play_sources),
                'vod_play_url': '$$$'.join(play_urls)
            }
            
            return {'list': [vod]}
            
        except Exception as e:
            self.log(f"获取详情内容失败: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        try:
            search_url = f"{self.host}/s/-------------/?wd={quote(key)}"
            response = self.fetch(search_url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            items = data('.module-card-item')

            for item in items.items():
                try:
                    # 提取视频ID和链接
                    detail_link = item('.module-card-item-poster').attr('href')
                    if not detail_link:
                        continue

                    vod_id = re.search(r'/nivod/(\d+)/', detail_link)
                    if not vod_id:
                        continue
                    vod_id = vod_id.group(1)

                    # 提取标题
                    title_elem = item('.module-card-item-title strong')
                    vod_name = title_elem.text().strip()
                    if not vod_name:
                        title_elem = item('.module-card-item-title a')
                        vod_name = title_elem.text().strip()

                    # 提取图片
                    img_elem = item('.module-item-pic img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        vod_pic = self.host + vod_pic

                    # 提取备注（集数信息）
                    vod_remarks = item('.module-item-note').text().strip()

                    # 提取年份
                    info_text = item('.module-info-item-content').eq(0).text()
                    vod_year = re.search(r'(\d{4})', info_text)
                    vod_year = vod_year.group(1) if vod_year else ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析搜索结果项失败: {e}")
                    continue
            
            return {'list': videos, 'page': int(pg)}
            
        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {'list': [], 'page': int(pg)}

    def playerContent(self, flag, id, vipFlags):
        try:
            play_url = f"{self.host}{id}"
            response = self.fetch(play_url, headers=self.headers)
            content = response.text
            
            # 尝试从页面中提取播放地址
            # 方法1: 查找m3u8链接
            m3u8_pattern = r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)'
            m3u8_match = re.search(m3u8_pattern, content)
            
            if m3u8_match:
                video_url = m3u8_match.group(1)
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }
            
            # 方法2: 查找mp4链接
            mp4_pattern = r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)'
            mp4_match = re.search(mp4_pattern, content)
            
            if mp4_match:
                video_url = mp4_match.group(1)
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }
            
            # 方法3: 查找iframe中的播放器
            iframe_pattern = r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>'
            iframe_match = re.search(iframe_pattern, content)
            
            if iframe_match:
                iframe_url = iframe_match.group(1)
                if not iframe_url.startswith('http'):
                    iframe_url = self.host + iframe_url
                
                # 获取iframe内容
                iframe_response = self.fetch(iframe_url, headers=self.headers)
                iframe_content = iframe_response.text
                
                # 在iframe中查找播放地址
                m3u8_match = re.search(m3u8_pattern, iframe_content)
                if m3u8_match:
                    video_url = m3u8_match.group(1)
                    return {
                        "parse": 0,
                        "url": video_url,
                        "header": self.headers
                    }
                
                mp4_match = re.search(mp4_pattern, iframe_content)
                if mp4_match:
                    video_url = mp4_match.group(1)
                    return {
                        "parse": 0,
                        "url": video_url,
                        "header": self.headers
                    }
            
            # 如果都没找到，返回原始播放页面让系统解析
            return {
                "parse": 1,
                "url": play_url,
                "header": self.headers
            }
            
        except Exception as e:
            self.log(f"获取播放地址失败: {e}")
            return {
                "parse": 1,
                "url": f"{self.host}{id}",
                "header": self.headers
            }

    def getpq(self, data):
        """获取pyquery对象"""
        try:
            return pq(data)
        except Exception as e:
            self.log(f"解析HTML失败: {e}")
            return pq("")
