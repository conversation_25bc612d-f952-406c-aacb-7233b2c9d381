# -*- coding: utf-8 -*-
# 分析NO视频详情页和播放页面结构
import requests
import re
from pyquery import PyQuery as pq

def analyze_detail_page():
    """分析NO视频的详情页面结构"""
    
    # 测试URL - 使用之前测试中的一个视频
    test_url = 'https://www.novipnoad.net/tv/western/150717.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',  # 禁用压缩
    }
    
    print("=" * 60)
    print("分析NO视频详情页面结构")
    print(f"测试URL: {test_url}")
    print("=" * 60)
    
    try:
        response = requests.get(test_url, headers=headers, timeout=15)
        print(f"状态码: {response.status_code}")
        print(f"页面大小: {len(response.text)} 字符")
        
        if response.status_code != 200:
            print("访问失败")
            return
        
        data = pq(response.text)
        
        # 分析页面标题
        print("\n1. 页面标题分析:")
        page_title = data('title').text()
        print(f"完整标题: {page_title}")
        
        h1_title = data('h1').text()
        print(f"H1标题: {h1_title}")
        
        entry_title = data('.entry-title').text()
        print(f"Entry标题: {entry_title}")
        
        # 分析视频信息
        print("\n2. 视频信息分析:")
        
        # 查找可能的视频信息容器
        info_selectors = [
            '.entry-content', '.post-content', '.video-info', 
            '.movie-info', '.content', '.main-content'
        ]
        
        for selector in info_selectors:
            elements = data(selector)
            if len(elements) > 0:
                print(f"找到信息容器: {selector} ({len(elements)}个)")
                content = elements.eq(0).text()[:200]
                print(f"内容预览: {content}...")
        
        # 分析播放器结构
        print("\n3. 播放器结构分析:")
        
        # 查找可能的播放器容器
        player_selectors = [
            'iframe', '.player', '.video-player', '.embed-responsive',
            '.player-container', '.video-container', '.embed-container'
        ]
        
        for selector in player_selectors:
            elements = data(selector)
            if len(elements) > 0:
                print(f"找到播放器元素: {selector} ({len(elements)}个)")
                for i, elem in enumerate(elements.items()):
                    if i < 3:  # 只显示前3个
                        src = elem.attr('src')
                        if src:
                            print(f"  播放源 {i+1}: {src}")
                        else:
                            html = str(elem)[:150]
                            print(f"  HTML {i+1}: {html}...")
        
        # 查找JavaScript中的播放地址
        print("\n4. JavaScript播放地址分析:")
        
        # 查找script标签
        scripts = data('script')
        print(f"找到 {len(scripts)} 个script标签")
        
        video_patterns = [
            r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
            r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
            r'["\']url["\']:\s*["\']([^"\']+)["\']',
            r'src:\s*["\']([^"\']+)["\']',
            r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        found_urls = set()
        for script in scripts.items():
            script_content = script.text()
            if script_content and len(script_content) > 50:
                for pattern in video_patterns:
                    matches = re.findall(pattern, script_content)
                    for match in matches:
                        if 'http' in match and ('.m3u8' in match or '.mp4' in match):
                            found_urls.add(match)
        
        if found_urls:
            print("找到的可能播放地址:")
            for i, url in enumerate(found_urls):
                print(f"  {i+1}. {url}")
        else:
            print("未在JavaScript中找到明显的播放地址")
        
        # 分析下载链接
        print("\n5. 下载链接分析:")
        download_links = data('a[href*=".mp4"], a[href*=".m3u8"], a[href*="download"], a[href*="play"]')
        print(f"找到 {len(download_links)} 个可能的下载/播放链接")
        
        for i, link in enumerate(download_links.items()):
            if i < 5:  # 只显示前5个
                href = link.attr('href')
                text = link.text().strip()
                print(f"  {i+1}. {text}: {href}")
        
        # 保存页面源码用于进一步分析
        with open('novipnoad_detail_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n页面源码已保存到: novipnoad_detail_page.html")
        
        # 分析标题清理需求
        print("\n6. 标题清理分析:")
        titles_to_analyze = [page_title, h1_title, entry_title]
        
        for title in titles_to_analyze:
            if title:
                print(f"原标题: {title}")
                
                # 分析需要清理的部分
                patterns_to_remove = [
                    r'【[^】]*】',  # 方括号内容
                    r'\([^)]*\)',  # 圆括号内容
                    r'NO视频.*?$',  # NO视频相关
                    r'www\.novipnoad\.net.*?$',  # 网站域名
                    r'–.*?$',  # 破折号后内容
                    r'-.*?$',   # 减号后内容
                ]
                
                cleaned = title
                for pattern in patterns_to_remove:
                    cleaned = re.sub(pattern, '', cleaned).strip()
                
                print(f"清理后: {cleaned}")
                print()
        
        print("=" * 60)
        print("详情页面分析完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_detail_page()
