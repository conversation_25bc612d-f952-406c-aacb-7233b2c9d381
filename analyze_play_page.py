# -*- coding: utf-8 -*-
# 分析播放页面结构，查找真实播放地址
import requests
import re
import json
from pyquery import PyQuery as pq
from urllib.parse import urljoin, urlparse, parse_qs

def analyze_play_page():
    """深入分析播放页面结构"""
    play_url = 'http://www.bimiacg11.net/bangumi/38328/play/1/1/'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'http://www.bimiacg11.net/',
        'Upgrade-Insecure-Requests': '1'
    }

    print("=" * 60)
    print("分析播放页面结构")
    print("=" * 60)

    try:
        # 1. 获取播放页面内容
        print(f"\n1. 获取播放页面: {play_url}")
        response = requests.get(play_url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            content = response.text
            data = pq(content)

            # 2. 查找iframe
            print("\n2. 分析iframe结构:")
            iframes = data('iframe')
            for i, iframe in enumerate(iframes.items()):
                src = iframe.attr('src')
                id_attr = iframe.attr('id')
                print(f"  iframe {i+1}: src={src}, id={id_attr}")

            # 3. 查找JavaScript中的播放地址
            print("\n3. 分析JavaScript代码:")

            # 查找script标签
            scripts = data('script')
            for i, script in enumerate(scripts.items()):
                script_content = script.html()
                if script_content and ('play' in script_content.lower() or 'url' in script_content.lower()):
                    print(f"  Script {i+1} (前200字符): {script_content[:200]}...")

            # 4. 查找可能的播放地址模式
            print("\n4. 查找播放地址模式:")
            patterns = [
                r'src["\s]*[:=]["\s]*([^"]*\.(?:m3u8|mp4|flv)[^"]*)',
                r'url["\s]*[:=]["\s]*([^"]*\.(?:m3u8|mp4|flv)[^"]*)',
                r'video["\s]*[:=]["\s]*([^"]*\.(?:m3u8|mp4|flv)[^"]*)',
                r'play["\s]*[:=]["\s]*([^"]*\.(?:m3u8|mp4|flv)[^"]*)',
                r'https?://[^"\s]*\.(?:m3u8|mp4|flv)[^"\s]*',
                r'/static/danmu/play\.php\?[^"]*',
                r'danmu[^"]*\.php[^"]*'
            ]

            found_urls = []
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    found_urls.extend(matches)
                    print(f"  模式 '{pattern}' 找到: {matches}")

            # 5. 查找特殊的播放器相关URL
            print("\n5. 查找播放器相关URL:")
            special_patterns = [
                r'(/static/[^"]*play[^"]*\.php[^"]*)',
                r'(play\.php\?[^"]*)',
                r'(/bangumi/[^"]*)',
                r'(danmu[^"]*\.php[^"]*)'
            ]

            for pattern in special_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"  特殊模式 '{pattern}' 找到: {matches}")
                    for match in matches:
                        full_url = urljoin(play_url, match)
                        print(f"    完整URL: {full_url}")

            # 6. 查找可能的API调用
            print("\n6. 查找API调用:")
            api_patterns = [
                r'ajax[^"]*',
                r'api[^"]*',
                r'\.php\?[^"]*',
                r'/[^"]*\?[^"]*url[^"]*'
            ]

            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    unique_matches = list(set(matches))[:5]  # 去重并限制数量
                    print(f"  API模式 '{pattern}' 找到: {unique_matches}")

            # 7. 分析页面中的变量定义
            print("\n7. 分析JavaScript变量:")
            var_patterns = [
                r'var\s+(\w*(?:url|src|play|video)\w*)\s*=\s*["\']([^"\']+)["\']',
                r'(\w*(?:url|src|play|video)\w*)\s*[:=]\s*["\']([^"\']+)["\']'
            ]

            for pattern in var_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for var_name, var_value in matches:
                        print(f"  变量: {var_name} = {var_value}")

            # 8. 分析播放器配置文件
            print("\n8. 分析播放器配置文件:")
            player_config_urls = [
                '/static/js/playerconfig.js',
                '/static/js/player.js'
            ]

            for config_url in player_config_urls:
                try:
                    full_config_url = urljoin(play_url, config_url)
                    print(f"  获取配置文件: {full_config_url}")
                    config_response = requests.get(full_config_url, headers=headers, timeout=5)
                    if config_response.status_code == 200:
                        config_content = config_response.text
                        print(f"    配置文件大小: {len(config_content)} 字符")

                        # 查找播放器相关配置
                        config_patterns = [
                            r'url["\s]*[:=]["\s]*([^"\']+)',
                            r'src["\s]*[:=]["\s]*([^"\']+)',
                            r'play["\s]*[:=]["\s]*([^"\']+)',
                            r'danmu[^"\']*',
                            r'\.php[^"\']*'
                        ]

                        for pattern in config_patterns:
                            matches = re.findall(pattern, config_content, re.IGNORECASE)
                            if matches:
                                unique_matches = list(set(matches))[:3]  # 去重并限制数量
                                print(f"    配置模式 '{pattern}' 找到: {unique_matches}")
                    else:
                        print(f"    配置文件获取失败: {config_response.status_code}")
                except Exception as e:
                    print(f"    配置文件分析失败: {e}")

            # 9. 分析player_aaaa变量
            print("\n9. 分析player_aaaa变量:")
            player_pattern = r'var\s+player_aaaa\s*=\s*({[^}]+})'
            player_match = re.search(player_pattern, content)
            if player_match:
                try:
                    player_json_str = player_match.group(1)
                    print(f"  找到player_aaaa: {player_json_str}")

                    # 尝试解析JSON
                    # 先处理Unicode转义
                    import json
                    player_data = json.loads(player_json_str)
                    print(f"  解析结果: {player_data}")
                except Exception as e:
                    print(f"  JSON解析失败: {e}")

        print("\n" + "=" * 60)
        print("播放页面分析完成")
        print("=" * 60)

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_play_page()
