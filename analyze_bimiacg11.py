# -*- coding: utf-8 -*-
# 分析 bimiacg11.net 网站结构
import requests
import re
import json
from pyquery import PyQuery as pq
from urllib.parse import urljoin, urlparse

def analyze_website():
    """分析网站结构"""
    base_url = 'http://www.bimiacg11.net/'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }

    print("=" * 60)
    print("开始分析 bimiacg11.net 网站结构")
    print("=" * 60)

    try:
        # 1. 分析主页
        print("\n1. 分析主页结构...")
        response = requests.get(base_url, headers=headers, timeout=10)
        print(f"主页状态码: {response.status_code}")
        print(f"主页URL: {response.url}")

        if response.status_code == 200:
            data = pq(response.text)

            # 分析导航菜单
            print("\n导航菜单分析:")
            nav_items = data('nav a, .nav a, .menu a, .navigation a')
            for i, item in enumerate(nav_items.items()):
                href = item.attr('href')
                text = item.text().strip()
                if href and text:
                    full_url = urljoin(base_url, href)
                    print(f"  {i+1}. {text} -> {full_url}")

            # 分析视频列表容器
            print("\n视频列表容器分析:")
            selectors = [
                '.module-item', '.video-item', '.movie-item', '.card', '.list-item',
                '.item', '.video', '.movie', '.content-item', '.post-item',
                '.entry', '.article', '.media-item', '.thumb'
            ]

            for selector in selectors:
                items = data(selector)
                if len(items) > 0:
                    print(f"  找到容器: {selector} (数量: {len(items)})")

                    # 分析第一个项目的结构
                    first_item = items.eq(0)
                    print(f"    第一个项目HTML: {first_item.outer_html()[:200]}...")

                    # 查找链接
                    links = first_item.find('a')
                    for link in links.items():
                        href = link.attr('href')
                        text = link.text().strip()
                        if href:
                            full_url = urljoin(base_url, href)
                            print(f"    链接: {text} -> {full_url}")

                    # 查找图片
                    images = first_item.find('img')
                    for img in images.items():
                        src = img.attr('src') or img.attr('data-src') or img.attr('data-original')
                        alt = img.attr('alt')
                        if src:
                            full_src = urljoin(base_url, src)
                            print(f"    图片: {alt} -> {full_src}")

                    break

            # 分析详情页链接模式
            print("\n详情页链接模式分析:")
            detail_patterns = [
                r'/detail/(\d+)', r'/video/(\d+)', r'/movie/(\d+)',
                r'/show/(\d+)', r'/play/(\d+)', r'/v/(\d+)',
                r'/anime/(\d+)', r'/dongman/(\d+)'
            ]

            all_links = data('a[href]')
            detail_links = []

            for link in all_links.items():
                href = link.attr('href')
                if href:
                    for pattern in detail_patterns:
                        if re.search(pattern, href):
                            detail_links.append(href)
                            break

            if detail_links:
                print(f"  找到 {len(detail_links)} 个详情页链接")
                for i, link in enumerate(detail_links[:5]):  # 只显示前5个
                    full_url = urljoin(base_url, link)
                    print(f"    {i+1}. {full_url}")

            # 分析分页
            print("\n分页结构分析:")
            page_selectors = [
                '.pagination a', '.page a', '.pager a', '.page-link',
                '.next', '.prev', '.page-number'
            ]

            for selector in page_selectors:
                page_items = data(selector)
                if len(page_items) > 0:
                    print(f"  找到分页元素: {selector} (数量: {len(page_items)})")
                    for item in page_items.items():
                        href = item.attr('href')
                        text = item.text().strip()
                        if href:
                            full_url = urljoin(base_url, href)
                            print(f"    {text} -> {full_url}")

        # 2. 分析搜索功能
        print("\n\n2. 分析搜索功能...")
        search_forms = data('form[action*="search"], form[action*="s/"], input[name="wd"], input[name="q"], input[name="keyword"]')
        if len(search_forms) > 0:
            print(f"  找到搜索表单: {len(search_forms)} 个")
            for form in search_forms.items():
                action = form.attr('action')
                method = form.attr('method') or 'GET'
                if action:
                    full_action = urljoin(base_url, action)
                    print(f"    表单: {method} {full_action}")

                # 分析表单字段
                inputs = form.find('input')
                for inp in inputs.items():
                    name = inp.attr('name')
                    input_type = inp.attr('type')
                    if name:
                        print(f"      字段: {name} (类型: {input_type})")

        # 3. 尝试访问一些常见的分类页面
        print("\n\n3. 分析分类页面...")
        category_urls = [
            '/dongman/', '/anime/', '/cartoon/', '/comic/',
            '/movie/', '/tv/', '/variety/', '/show/',
            '/type/1/', '/type/2/', '/type/3/', '/type/4/',
            '/category/1/', '/cat/1/', '/list/1/'
        ]

        for cat_url in category_urls:
            try:
                full_cat_url = urljoin(base_url, cat_url)
                cat_response = requests.get(full_cat_url, headers=headers, timeout=5)
                if cat_response.status_code == 200:
                    print(f"  有效分类页: {full_cat_url}")

                    # 简单分析分类页结构
                    cat_data = pq(cat_response.text)
                    items = cat_data('.module-item, .video-item, .movie-item, .item')
                    if len(items) > 0:
                        print(f"    找到视频项: {len(items)} 个")

                    # 只分析前几个，避免过多请求
                    break
            except:
                continue

        print("\n" + "=" * 60)
        print("网站结构分析完成")
        print("=" * 60)

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_website()
